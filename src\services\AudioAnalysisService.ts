import { makeAutoObservable, runInAction } from 'mobx';
import { RootStoreType } from '../stores/RootStore';
import { TrackInfo } from './DatabaseService';
// Placeholder for shared AudioContext access
import { getSharedAudioContext } from '@/services/AudioManager'; // Use path alias

// Define the structure for analysis results expected from the worker
interface AnalysisResults {
    bpm?: number;
    key?: string;
    danceability?: number;
    // Add other fields corresponding to the worker's output
    // Add other fields corresponding to the worker's output
    // e.g., firstBeatTime?: number; beatGridTimes?: number[];
    error?: string;
}

// Define the structure for messages sent *to* the worker
interface WorkerPostMessageData {
    action: 'analyze';
    audioData: Float32Array;
    sampleRate: number;
    trackId: string; // Track ID is string
}

// Define the structure for messages received *from* the worker
interface WorkerResponseMessage {
    status?: 'ready';
    trackId?: string; // Track ID is string
    results?: AnalysisResults;
    error?: string; // Worker can also send top-level errors
}

/**
 * Retrieves the FileSystemFileHandle for a given track ID.
 * It searches through active tracked directories to find the file.
 * @param rootStore - The root MobX store instance.
 * @param trackId - The unique ID of the track.
 * Retrieves the FileSystemFileHandle for a given track ID using its associated directory ID.
 * @param rootStore - The root MobX store instance.
 * @param trackId - The unique ID of the track.
 * @returns A Promise resolving to the FileSystemFileHandle or null if not found or an error occurs.
 */
export async function getTrackFileHandle(rootStore: RootStoreType, trackId: string): Promise<FileSystemFileHandle | null> {
    console.log(`getTrackFileHandle: Attempting to get handle for track ${trackId}`);
    try {
        // Check if rootStore and databaseService exist
        if (!rootStore) {
            console.error(`getTrackFileHandle: Root store is undefined`);
            return null;
        }
        
        if (!rootStore.databaseService) {
            console.error(`getTrackFileHandle: Database service is undefined in root store`);
            return null;
        }

        // 1. Get Track Info including directoryId
        const track = await rootStore.databaseService.getTrack(trackId);
        if (!track) {
            console.error(`getTrackFileHandle: Track info not found for track ${trackId}`);
            return null;
        }
        if (!track.filePath) {
            console.error(`getTrackFileHandle: Track info for ${trackId} is missing filePath.`);
            return null;
        }
        if (!track.directoryId) {
            console.error(`getTrackFileHandle: Track info for ${trackId} is missing directoryId. Cannot locate file handle.`);
            // Potentially fall back to the old method here if desired, or just fail.
            return null;
        }

        // 2. Get the Specific Tracked Directory using directoryId
        const dirInfo = await rootStore.databaseService.getTrackedDirectory(track.directoryId);
        if (!dirInfo) {
            console.error(`getTrackFileHandle: Tracked directory with ID ${track.directoryId} not found for track ${trackId}.`);
            return null;
        }
        if (!dirInfo.handle) {
            console.error(`getTrackFileHandle: Tracked directory ${dirInfo.name} (ID: ${dirInfo.id}) has a missing or invalid handle.`);
            return null;
        }
        // Optional: Check if the directory is still active? Depends on requirements.
        // if (!dirInfo.isActive) {
        //     console.warn(`getTrackFileHandle: Directory ${dirInfo.name} (ID: ${dirInfo.id}) is inactive. File handle might be stale.`);
        //     // Decide whether to proceed or return null
        // }

        // 3. Attempt to Get File Handle from the specific directory
        try {
            // Check permissions first
            const permissionStatus = await dirInfo.handle.queryPermission({ mode: 'read' });
            if (permissionStatus !== 'granted') {
                // Request permission if not already granted
                const newPermission = await dirInfo.handle.requestPermission({ mode: 'read' });
                if (newPermission !== 'granted') {
                    console.error(`getTrackFileHandle: Permission denied for directory ${dirInfo.name} (ID: ${dirInfo.id})`);
                    return null;
                }
            }

            const fileHandle = await dirInfo.handle.getFileHandle(track.filePath);
            console.log(`getTrackFileHandle: Found handle for track ${trackId} in directory ${dirInfo.name} (ID: ${dirInfo.id})`);
            return fileHandle;
        } catch (e: any) {
            if (e.name === 'NotFoundError') {
                console.error(`getTrackFileHandle: File ${track.filePath} not found within its associated directory ${dirInfo.name} (ID: ${dirInfo.id}). Path or directory association might be incorrect.`);
            } else if (e.name === 'NotAllowedError') {
                console.error(`getTrackFileHandle: Permission denied for file ${track.filePath} in directory ${dirInfo.name} (ID: ${dirInfo.id})`);
            } else {
                console.error(`getTrackFileHandle: Error getting file handle for ${track.filePath} in directory ${dirInfo.name} (ID: ${dirInfo.id}):`, e);
            }
            return null; // Return null if file handle couldn't be retrieved
        }

    } catch (error) {
        console.error(`getTrackFileHandle: Unexpected error getting file handle for track ${trackId}:`, error);
        return null;
    }
}


export class AudioAnalysisService {
    rootStore: RootStoreType;
    private worker: Worker | null = null;
    isAnalyzing: boolean = false;
    currentTrackId: string | null = null;
    queue: TrackInfo[] = [];
    analysisErrors: Map<string, string> = new Map();

    constructor(rootStore: RootStoreType) {
        this.rootStore = rootStore;
        makeAutoObservable(this);
        this.initializeWorker();
    }

    private initializeWorker() {
        // Ensure worker code is handled correctly by the build process (e.g., Vite's ?worker import)
        this.worker = new Worker(new URL('../workers/audioAnalyzer.worker.ts', import.meta.url), {
            type: 'module'
        });

        this.worker.onmessage = (event: MessageEvent<WorkerResponseMessage>) => {
            this.handleWorkerMessage(event.data);
        };

        this.worker.onerror = (error) => {
            console.error("AudioAnalysisService: Worker error:", error);
            const errorMessage = `Worker error: ${error.message}`;
            runInAction(() => {
                if (this.currentTrackId !== null) {
                    this.analysisErrors.set(this.currentTrackId, errorMessage);
                    // Update DB status to 'Error'
                    this.rootStore.databaseService.updateTrackAnalysisStatus(this.currentTrackId, 'Error')
                        .catch(dbErr => console.error(`AudioAnalysisService: Failed to update status to 'Error' after worker error for track ${this.currentTrackId}:`, dbErr));
                    this.currentTrackId = null;
                }
                this.isAnalyzing = false;
                // Consider if we need to process the next item or stop
                // We don't call processQueue here anymore, as the 'ready' message will trigger it.
            });
        };

        // Send an initialization message to the worker
        console.log("AudioAnalysisService: Sending 'init' message to worker.");
        this.worker.postMessage({ action: 'init' });
    }

    private handleWorkerMessage(data: WorkerResponseMessage) {
        console.log("AudioAnalysisService: Received message from worker:", data);
        if (data.status === 'ready') {
            console.log("AudioAnalysisService: Worker is ready.");
            // Now that the worker is ready, we can start processing the queue
            this.processQueue();
        } else if (data.trackId) {
            const { trackId } = data;
            // Ensure we are still expecting results for this track
            if (trackId === this.currentTrackId) {
                if (data.results) {
                    this.handleAnalysisResult(trackId, data.results);
                } else if (data.error) {
                    // Handle error reported from within the worker's analysis process
                    console.error(`AudioAnalysisService: Analysis error from worker for track ${trackId}: ${data.error}`);
                    runInAction(() => {
                        this.analysisErrors.set(trackId, data.error || 'Unknown worker analysis error');
                    });
                    // Update DB status to 'Error'
                    this.rootStore.databaseService.updateTrackAnalysisStatus(trackId, 'Error')
                         .catch(dbErr => console.error(`AudioAnalysisService: Failed to update status to 'Error' after worker analysis error for track ${trackId}:`, dbErr));
                }
                 // Mark current track as finished and process next
                 runInAction(() => {
                    this.currentTrackId = null;
                    this.isAnalyzing = false;
                });
                this.processQueue();
            } else {
                 console.warn(`AudioAnalysisService: Received result for unexpected track ID ${trackId} (expected ${this.currentTrackId}). Ignoring.`);
            }
        } else {
            console.warn("AudioAnalysisService: Received unknown message structure from worker:", data);
        }
    }

    private async handleAnalysisResult(trackId: string, results: AnalysisResults) {
        console.log(`AudioAnalysisService: Handling results for track ${trackId}`, results);
        try {
            if (results.error) { // Check for error within the results object itself
                 console.error(`AudioAnalysisService: Analysis error reported in results for track ${trackId}: ${results.error}`);
                 runInAction(() => {
                    this.analysisErrors.set(trackId, results.error || 'Unknown analysis error');
                  });
                  await this.rootStore.databaseService.updateTrackAnalysisStatus(trackId, 'Error');

                  // Update track in MST store
                  this.updateTrackInStore(trackId, 'Error');
             } else {
                 // Update the track in the database with the analysis results and set status to 'Analyzed'
                 await this.rootStore.databaseService.updateTrackAnalysisData(trackId, results);
                 console.log(`AudioAnalysisService: Successfully updated analysis data and status for track ${trackId}`);

                 // Update track in MST store and load analysis data
                 this.updateTrackInStore(trackId, 'Analyzed');
             }
         } catch (dbError: any) {
             console.error(`AudioAnalysisService: Failed to update database for track ${trackId}:`, dbError);
             try {
                 await this.rootStore.databaseService.updateTrackAnalysisStatus(trackId, 'Error');

                 // Update track in MST store
                 this.updateTrackInStore(trackId, 'Error');
             } catch (statusUpdateError) {
                 console.error(`AudioAnalysisService: Also failed to update status to Error for track ${trackId}:`, statusUpdateError);
             }
             runInAction(() => {
                this.analysisErrors.set(trackId, `DB update failed: ${dbError?.message || dbError}`);
             });
        }
    }

    async startAnalysis() {
        // Only fetch tracks if not already analyzing and worker exists
        if (this.isAnalyzing || !this.worker) {
            console.log("AudioAnalysisService: Analysis already running or worker not ready.");
            return;
        }

         console.log("AudioAnalysisService: Starting analysis process...");
         try {
             const tracksToAnalyze = await this.rootStore.databaseService.getTracksForAnalysis();
             if (tracksToAnalyze.length === 0) {
                 console.log("AudioAnalysisService: No tracks found waiting for analysis.");
                 return;
            }
            runInAction(() => {
                // Add only tracks not already in the queue or being analyzed
                const currentIds = new Set([...this.queue.map(t => t.id), this.currentTrackId]);
                const newTracks = tracksToAnalyze.filter(t => t.id && !currentIds.has(t.id));
                this.queue.push(...newTracks);
                this.analysisErrors.clear();
            });
            console.log(`AudioAnalysisService: Added ${tracksToAnalyze.length} tracks to analyze queue (current size: ${this.queue.length}).`);
            // processQueue is not called automatically, trying to add it here.
            this.processQueue();

        } catch (error) {
            console.error("AudioAnalysisService: Error fetching tracks for analysis:", error);
        }
    }

    private async processQueue() {
        // Only process if worker is ready, not already analyzing, and queue is not empty
        if (!this.worker || this.isAnalyzing || this.queue.length === 0) {
            if (this.queue.length === 0 && !this.isAnalyzing) {
                console.log("AudioAnalysisService: Analysis queue is empty. Process finished.");
            }
            return;
        }

        const track = this.queue.shift();

        if (!track || !track.id || !track.filePath) {
            console.warn("AudioAnalysisService: Skipped invalid track data in queue:", track);
            this.processQueue(); // Immediately try the next item
            return;
        }

        runInAction(() => {
            this.isAnalyzing = true;
            this.currentTrackId = track.id!;
        });
        console.log(`AudioAnalysisService: Starting processing for track ${track.id} (${track.filePath})`);

        try {
            // 1. Get File Handle (Replace placeholder)
            const fileHandle = await getTrackFileHandle(this.rootStore, track.id);
            if (!fileHandle) {
                throw new Error(`Could not retrieve file handle for track ${track.id}`);
            }

            // 2. Get File and Read ArrayBuffer
            const file = await fileHandle.getFile();
            const arrayBuffer = await file.arrayBuffer();

            // 3. Decode Audio Data
            const audioContext = getSharedAudioContext(); // Get shared context
            let audioBuffer: AudioBuffer;
            try {
                audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            } catch (decodeError) {
                 console.error(`AudioAnalysisService: Error decoding audio data for track ${track.id}:`, decodeError);
                 throw new Error(`Audio decoding failed: ${(decodeError as Error).message}`);
            }

            // 4. Extract Channel Data (e.g., first channel)
            if (audioBuffer.numberOfChannels === 0) {
                throw new Error("AudioBuffer has no channels.");
            }
            const channelData = audioBuffer.getChannelData(0); // Use the first channel
            const sampleRate = audioBuffer.sampleRate;

            // 5. Send to Worker (using Transferable Objects)
            console.log(`AudioAnalysisService: Sending track ${track.id} data to worker.`);
            const message: WorkerPostMessageData = {
                action: 'analyze',
                audioData: channelData,
                sampleRate: sampleRate,
                trackId: track.id,
            };
            this.worker.postMessage(message, [channelData.buffer]); // Transfer the buffer

            // 6. Update DB Status to 'Analyzing'
            await this.rootStore.databaseService.updateTrackAnalysisStatus(track.id, 'Analyzing');

        } catch (error: any) {
            console.error(`AudioAnalysisService: Error processing track ${track.id}:`, error);
            runInAction(() => {
                this.analysisErrors.set(track.id!, `Processing error: ${error.message}`);
                this.isAnalyzing = false;
                this.currentTrackId = null;
            });
             // Update DB status to 'Error'
             try {
                await this.rootStore.databaseService.updateTrackAnalysisStatus(track.id!, 'Error');
             } catch (dbErr) {
                 console.error(`AudioAnalysisService: Failed to update status to 'Error' after processing error for track ${track.id}:`, dbErr);
             }
            this.processQueue(); // Try the next track
        }
    }

    stopAnalysis() {
        console.log("AudioAnalysisService: Stopping analysis process (clearing queue).");
        runInAction(() => {
            this.queue = [];
            // Note: This doesn't stop the *currently* analyzing track in the worker.
            // A 'cancel' message mechanism would be needed for that.
        });
    }

    addTrackToQueue(track: TrackInfo) {
        if (track && track.id && !this.queue.some(t => t.id === track.id) && this.currentTrackId !== track.id) {
             runInAction(() => {
                this.queue.push(track);
             });
             console.log(`AudioAnalysisService: Added track ${track.id} to analysis queue.`);
             this.processQueue(); // Start processing if not already running
        }
    }

    dispose() {
        console.log("AudioAnalysisService: Disposing worker.");
        this.stopAnalysis();
        this.worker?.terminate();
        this.worker = null;
    }

    // Add new method to update track in MST store
    private updateTrackInStore(trackId: string, status: TrackInfo['analysisStatus']) {
        // Get the library store from root store
        const libraryStore = this.rootStore.libraryStore;
        if (!libraryStore) {
            console.error(`AudioAnalysisService: Cannot update track ${trackId} in store: Library store not available`);
            return;
        }

        // Find the track in the store
        const track = libraryStore.tracks.get(trackId);
        if (!track) {
            console.error(`AudioAnalysisService: Cannot update track ${trackId} in store: Track not found`);
            return;
        }

        track.analysisFinished(status);
    }
}
