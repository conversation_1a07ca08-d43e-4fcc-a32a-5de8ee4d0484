# Custom High-Performance Waveform Component

## Overview

The custom waveform component replaces the previous Wavesurfer.js implementation with a high-performance, Canvas-based visualization system designed specifically for DJ applications.

## Key Features

### Performance Optimizations
- **Web Worker Processing**: Audio analysis and waveform data generation is offloaded to a dedicated Web Worker to prevent UI blocking
- **Canvas Rendering**: Uses Canvas 2D API for efficient, hardware-accelerated rendering
- **High DPI Support**: Automatically scales for high-resolution displays
- **Debounced Resize**: Optimized resize handling to prevent excessive re-renders
- **Performance Monitoring**: Built-in performance metrics tracking

### Visual Modes

#### 1. Amplitude Waveform
- Traditional peaks/valleys visualization
- Single blue color scheme
- Shows overall audio dynamics

#### 2. Frequency-Based Waveform (Default)
- Multi-layered frequency band visualization
- Four frequency bands: Low, Mid-Low, Mid-High, High
- Color-coded frequency representation:
  - **Dark Blue**: Low frequencies (60 Hz)
  - **Medium Blue**: Mid-low frequencies (250 Hz)
  - **Light Blue**: Mid-high frequencies (4 kHz)
  - **Very Light Blue**: High frequencies (16 kHz)

### Integration Features
- **Click-to-Seek**: Click anywhere on the waveform to seek to that position
- **Real-time Playhead**: White playhead line shows current playback position
- **Responsive Design**: Automatically adapts to container size changes
- **State Persistence**: Integrates with existing deck state management

## Technical Architecture

### Components

#### 1. OverviewWaveformComponent.tsx
- Main React component
- Handles UI state and user interactions
- Manages Web Worker communication
- Renders Canvas-based waveform

#### 2. waveformProcessor.worker.ts
- Web Worker for audio processing
- Implements FFT analysis for frequency data
- Generates optimized waveform data structures
- Handles error recovery and validation

#### 3. waveformUtils.ts
- Utility functions for rendering and calculations
- Canvas setup and high-DPI handling
- Performance monitoring tools
- Color scheme definitions

### Data Flow

1. **Audio Loading**: Component receives audio buffer from DeckAudioEngine
2. **Worker Processing**: Audio data sent to Web Worker for analysis
3. **Data Generation**: Worker generates amplitude peaks and frequency band data
4. **Rendering**: Canvas renders waveform using processed data
5. **Interaction**: User clicks trigger seek operations via deck store

### Performance Characteristics

- **Processing Time**: Typically 50-200ms for 3-5 minute tracks
- **Memory Usage**: ~2-8MB per waveform (depending on track length)
- **Render Time**: <16ms per frame (60fps capable)
- **Worker Overhead**: Minimal impact on main thread

## Usage

### Basic Usage
```tsx
<OverviewWaveformComponent 
  deck={deckInstance} 
  height={80} 
  waveformStyle="frequency" 
/>
```

### Props
- `deck`: DeckStoreInstance - The deck instance to visualize
- `height?`: number - Height in pixels (default: 80)
- `waveformStyle?`: 'amplitude' | 'frequency' - Visualization mode (default: 'frequency')

## Configuration

### Color Customization
Colors can be customized in `waveformUtils.ts`:

```typescript
export const DEFAULT_COLORS = {
  amplitude: 'rgba(59, 130, 246, 0.6)',
  frequency: {
    low: 'rgba(30, 64, 175, 0.8)',
    midLow: 'rgba(59, 130, 246, 0.7)',
    midHigh: 'rgba(96, 165, 250, 0.6)',
    high: 'rgba(147, 197, 253, 0.5)',
  },
  playhead: '#ffffff',
};
```

### Performance Tuning
- Adjust `targetLength` in worker for resolution vs. performance trade-off
- Modify `debounce` delay for resize responsiveness
- Configure FFT size for frequency analysis accuracy

## Browser Compatibility

- **Chrome/Edge**: Full support with Web Workers and Canvas 2D
- **Firefox**: Full support
- **Safari**: Full support (iOS 11.3+)
- **Mobile**: Optimized for touch interactions

## Migration from Wavesurfer.js

The new component maintains the same interface as the previous Wavesurfer.js implementation:
- Same props structure
- Same integration with deck store
- Same click-to-seek functionality
- Improved performance and visual quality

## Future Enhancements

- WebGL rendering for even better performance
- Additional frequency band configurations
- Zoom functionality for detailed view
- Segment/marker overlay support
- Real-time spectrum analysis
- Custom color themes
