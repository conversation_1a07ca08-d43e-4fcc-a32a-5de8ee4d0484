import React, { useEffect, useRef, useState, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Card } from '@/components/ui/card';
import useWhyDidYouUpdate from '../useWhyDidYouUpdate';
import {
  WaveformData,
  renderWaveform,
  calculateSeekTime,
  setupHighDPICanvas,
  debounce,
  validateWaveformData,
  WaveformPerformanceMonitor
} from '@/utils/waveformUtils';

interface WaveformComponentProps {
  deck: DeckStoreInstance;
  height?: number;
  waveformStyle?: 'amplitude' | 'frequency';
}

const WaveformComponent: React.FC<WaveformComponentProps> = observer((props) => {
  const {
    deck,
    height = 80,
    waveformStyle = 'frequency',
  } = props;

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const workerRef = useRef<Worker | null>(null);
  const performanceMonitor = useRef(new WaveformPerformanceMonitor());

  const [isLoading, setIsLoading] = useState(false);
  const [waveformData, setWaveformData] = useState<WaveformData | null>(null);
  const [canvasWidth, setCanvasWidth] = useState(0);

  useWhyDidYouUpdate(`WaveformComponent (Deck: ${deck.id})`, {
    deck,
    height,
    waveformStyle,
  });

  // Initialize Web Worker
  useEffect(() => {
    workerRef.current = new Worker(
      new URL('../../workers/waveformProcessor.worker.ts', import.meta.url),
      { type: 'module' }
    );

    workerRef.current.onmessage = (event) => {
      const { type, data, error } = event.data;

      if (type === 'waveform-ready') {
        if (validateWaveformData(data)) {
          setWaveformData(data);
          setIsLoading(false);
        } else {
          console.error('Invalid waveform data received from worker');
          setIsLoading(false);
        }
      } else if (type === 'error') {
        console.error('Waveform worker error:', error);
        setIsLoading(false);
      }
    };

    workerRef.current.onerror = (error) => {
      console.error('Waveform worker error:', error);
      setIsLoading(false);
    };

    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
        workerRef.current = null;
      }
    };
  }, []);

  // Handle canvas resize with debouncing for performance
  const handleResize = useCallback(
    debounce(() => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setCanvasWidth(rect.width);
      }
    }, 16), // ~60fps
    []
  );

  // Set up resize observer
  useEffect(() => {
    const resizeObserver = new ResizeObserver(handleResize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
      handleResize(); // Initial size
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [handleResize]);

  // Load track data when track changes
  useEffect(() => {
    const loadTrackData = async () => {
      if (!deck.loadedTrack || !deck.audioEngine || !workerRef.current) return;

      setIsLoading(true);
      setWaveformData(null);

      try {
        // Ensure audio buffer is loaded
        if (!deck.audioEngine.isBufferLoaded()) {
          await deck.audioEngine.loadTrack(deck.loadedTrack.id);
        }

        const audioBuffer = deck.audioEngine.getAudioBuffer();
        if (audioBuffer) {
          // Send audio data to worker for processing
          const channelData = [];
          for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
            channelData.push(audioBuffer.getChannelData(i));
          }

          workerRef.current.postMessage({
            type: 'process-audio',
            data: {
              channelData,
              sampleRate: audioBuffer.sampleRate,
              duration: audioBuffer.duration,
              waveformStyle,
            }
          });
        }
      } catch (error) {
        console.error('Error loading track data for waveform:', error);
        setIsLoading(false);
      }
    };

    loadTrackData();
  }, [deck.loadedTrack, waveformStyle]);

  // Render waveform using utility function
  const renderWaveformCanvas = useCallback(() => {
    if (!canvasRef.current || !waveformData || canvasWidth === 0) return;

    const startTime = performance.now();
    const canvas = canvasRef.current;

    // Set up high DPI canvas
    const ctx = setupHighDPICanvas(canvas, canvasWidth, height);
    if (!ctx) return;

    // Render using utility function
    renderWaveform(
      ctx,
      waveformData,
      {
        width: canvasWidth,
        height,
        style: waveformStyle,
      },
      deck.currentTime,
      deck.id.replace('deck-', ''),
      deck.loadedTrack?.id
    );

    // Record performance metrics
    performanceMonitor.current.recordRenderTime(startTime);
  }, [canvasWidth, height, waveformData, waveformStyle, deck.currentTime]);

  // Render on data or time changes
  useEffect(() => {
    renderWaveformCanvas();
  }, [renderWaveformCanvas]);

  // Handle canvas click for seeking
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!deck.loadedTrack || !waveformData || !canvasRef.current) return;

    const seekTime = calculateSeekTime(
      event.nativeEvent,
      canvasRef.current,
      waveformData.duration
    );

    deck.seek(seekTime);
  }, [deck, waveformData]);

  return (
    <Card className="relative overflow-hidden">
      <div className="p-1">
        <div className="relative" ref={containerRef}>
          <canvas
            ref={canvasRef}
            className={`w-full cursor-pointer ${isLoading ? 'opacity-50' : 'opacity-100'} transition-opacity`}
            onClick={handleCanvasClick}
            style={{ height: `${height}px` }}
          />
        </div>

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/50">
            <span className="text-sm text-muted-foreground">Processing waveform...</span>
          </div>
        )}
      </div>
    </Card>
  );
});

export default WaveformComponent;