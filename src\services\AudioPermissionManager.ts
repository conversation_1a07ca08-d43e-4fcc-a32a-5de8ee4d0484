// src/services/AudioPermissionManager.ts

/**
 * Manages audio permission state and user interaction requirements
 * for browser autoplay policies compliance
 */

export type AudioPermissionState = 'pending' | 'granted' | 'denied';

export interface AudioPermissionManager {
  getPermissionState(): AudioPermissionState;
  requestPermission(): Promise<boolean>;
  isPermissionGranted(): boolean;
  onPermissionChange(callback: (state: AudioPermissionState) => void): () => void;
}

class AudioPermissionManagerImpl implements AudioPermissionManager {
  private permissionState: AudioPermissionState = 'pending';
  private listeners: Set<(state: AudioPermissionState) => void> = new Set();
  private hasUserInteracted = false;

  constructor() {
    // Check if we already have permission from a previous session
    this.checkExistingPermission();
  }

  private checkExistingPermission(): void {
    // Check if we have stored permission state
    const storedState = localStorage.getItem('audioPermissionGranted');
    if (storedState === 'true') {
      // Still need user interaction for this session
      this.permissionState = 'pending';
    }
  }

  public getPermissionState(): AudioPermissionState {
    return this.permissionState;
  }

  public isPermissionGranted(): boolean {
    return this.permissionState === 'granted';
  }

  public async requestPermission(): Promise<boolean> {
    if (this.permissionState === 'granted') {
      return true;
    }

    try {
      // Create a temporary audio context to test if user interaction allows audio
      const tempContext = new AudioContext();
      
      if (tempContext.state === 'suspended') {
        await tempContext.resume();
      }

      // If we get here without error, permission is granted
      await tempContext.close();
      
      this.permissionState = 'granted';
      this.hasUserInteracted = true;
      
      // Store permission for future reference
      localStorage.setItem('audioPermissionGranted', 'true');
      
      this.notifyListeners();
      return true;
    } catch (error) {
      console.error('Failed to get audio permission:', error);
      this.permissionState = 'denied';
      this.notifyListeners();
      return false;
    }
  }

  public onPermissionChange(callback: (state: AudioPermissionState) => void): () => void {
    this.listeners.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(callback => callback(this.permissionState));
  }

  public reset(): void {
    this.permissionState = 'pending';
    this.hasUserInteracted = false;
    localStorage.removeItem('audioPermissionGranted');
    this.notifyListeners();
  }
}

// Export singleton instance
export const audioPermissionManager = new AudioPermissionManagerImpl();
