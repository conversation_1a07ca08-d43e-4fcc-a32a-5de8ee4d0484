// src/components/AudioTestPage.tsx

import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import AudioDiagnostic from './AudioDiagnostic';
import MasterVolumeControl from '../Mixer/MasterVolumeControl';
import AudioRoutingTest from './AudioRoutingTest';
import { Card } from '../ui/card';
import { useStore } from '@/contexts/StoreContext';

export const AudioTestPage: React.FC = observer(() => {
  const rootStore = useStore();

  // Initialize audio routing manager when the test page loads
  useEffect(() => {
    const initializeAudioRouting = async () => {
      try {
        console.log('AudioTestPage: Initializing audio routing manager...');
        await rootStore.audioRoutingManager.initialize();
        console.log('AudioTestPage: Audio routing manager initialized successfully');
      } catch (error) {
        console.error('AudioTestPage: Failed to initialize audio routing manager:', error);
      }
    };

    initializeAudioRouting();

    // Cleanup on unmount
    return () => {
      try {
        console.log('AudioTestPage: Disposing audio routing manager...');
        rootStore.audioRoutingManager.dispose();
      } catch (error) {
        console.error('AudioTestPage: Failed to dispose audio routing manager:', error);
      }
    };
  }, [rootStore.audioRoutingManager]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Audio System Test & Diagnostic</h1>
        <p className="text-muted-foreground mt-2">
          Comprehensive testing and debugging tools for the DJ application audio system
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Master Volume Control */}
        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">Master Volume Control</h2>
          <MasterVolumeControl />
        </Card>

        {/* Audio Diagnostic */}
        <div className="lg:col-span-2">
          <AudioDiagnostic />
        </div>

        {/* Audio Routing Test */}
        <div className="lg:col-span-2">
          <AudioRoutingTest />
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-semibold text-blue-800 mb-2">Testing Instructions:</h3>
        <ol className="list-decimal list-inside space-y-1 text-blue-700 text-sm">
          <li>First, run the "Run Full Diagnostic" to check the audio system status</li>
          <li>Try the "Test Tone (Direct)" button to verify your speakers work</li>
          <li>Try the "Test Tone (Master)" button to test the master audio path</li>
          <li>Check the Master Volume Control levels - they should show audio activity</li>
          <li>If you have tracks loaded on decks, try playing them and watch the master levels</li>
          <li>Use the Audio Routing Test to verify individual components</li>
        </ol>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-semibold text-yellow-800 mb-2">Common Issues & Solutions:</h3>
        <ul className="list-disc list-inside space-y-1 text-yellow-700 text-sm">
          <li><strong>Audio Context Suspended:</strong> Click anywhere on the page to activate audio</li>
          <li><strong>No Master Levels:</strong> Check if deck paths are connected to master input</li>
          <li><strong>Direct Test Works, Master Doesn't:</strong> Master path connection issue</li>
          <li><strong>No Audio from Decks:</strong> Check if audio buffers are loaded and playing</li>
          <li><strong>Console Errors:</strong> Check browser console for detailed error messages</li>
        </ul>
      </div>
    </div>
  );
});

export default AudioTestPage;
