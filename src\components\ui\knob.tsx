import React, { useRef, useState, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'

export interface KnobProps {
  min?: number
  max?: number
  value?: number
  defaultValue?: number
  step?: number
  speed?: number
  acceleration?: number
  size?: "sm" | "md" | "lg"
  color?: string
  label?: string
  unit?: string
  startPosition?: number
  endPosition?: number
  hasStop?: boolean
  stopValue?: number
  decimals?: number
  enableDoubleClick?: boolean
  onChange?: (value: number) => void
  className?: string
  disabled?: boolean
}

// Inner component that just renders the knob
const KnobRenderer = React.memo(({
  value,
  min,
  max,
  size,
  color,
  hasStop,
  startPosition,
  endPosition,
  stopValue,
  isAtStop,
  disabled,
}: {
  value: number
  min: number
  max: number
  size: "sm" | "md" | "lg"
  color: string
  hasStop: boolean
  startPosition: number
  endPosition: number
  stopValue: number
  isAtStop: boolean
  disabled: boolean
}) => {
  // Helper function to get the actual color value
  // Assuming your modified version now passes "bg-lime-500" through directly:
  const getColorValue = (colorProp: string) => {
    if (colorProp.startsWith("var-")) {
      const colorName = colorProp.substring(4);
      return `var(--${colorName})`;
    }
    // Passes through "bg-lime-500", "#FF0000", "hsl(var(--primary))", etc.
    return colorProp;
  };

  const actualColor = getColorValue(color);

  // Convert value to angle for visual rotation
  const valueToAngle = (val: number) => {
    // Convert clock positions to angles (12 o'clock is 0 degrees, 3 o'clock is 90 degrees)
    const startAngle = ((startPosition - 12) * 30) % 360
    const endAngle = ((endPosition - 12) * 30) % 360
    
    // Calculate total angle range, handling wraparound
    let angleRange = endAngle - startAngle
    if (angleRange <= 0) angleRange += 360
    
    // Map value to angle
    const ratio = (val - min) / (max - min)
    return startAngle + (ratio * angleRange)
  };

  // Size classes
  const sizeClasses = {
    sm: "w-16 h-16",
    md: "w-24 h-24",
    lg: "w-32 h-32"
  }

  const indicatorSizeClasses = {
    sm: "w-1 h-5",
    md: "w-1.5 h-7",
    lg: "w-2 h-9"
  }

  // Calculate the position of the stop marker
  const getStopMarkerPosition = () => {
    const angle = ((stopValue - 12) * 30) % 360
    const radius = size === "sm" ? 8 : size === "md" ? 12 : 16

    // Calculate position using trigonometry
    const radians = (angle * Math.PI) / 180
    const x = Math.sin(radians) * radius
    const y = -Math.cos(radians) * radius

    return { x, y }
  }

  const stopMarkerPosition = getStopMarkerPosition()

  return (
    <div 
      className={cn(
        "absolute inset-0 rounded-full bg-zinc-900 border-4 border-zinc-800 shadow-lg",
        isAtStop && "ring-1 ring-destructive"
      )}
    >
      {/* Knob outer ring with gradient */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-b from-zinc-700 to-zinc-900"></div>

      {/* Knob inner circle */}
      <div className="absolute inset-2 rounded-full bg-zinc-800 flex items-center justify-center">
        {/* Concentric rings for texture */}
        <div className="absolute inset-3 rounded-full border border-zinc-700"></div>
        <div className="absolute inset-5 rounded-full border border-zinc-700"></div>
      </div>

      {/* Stop marker (only visible when hasStop is true AND we're at the stop position) */}
      {hasStop && isAtStop && (
        <div
          className="absolute w-1 h-1 bg-destructive rounded-full z-10"
          style={{
            left: "50%",
            top: "-11px",
            transform: "translateX(-50%)",
          }}
        />
      )}

      {/* Indicator line */}
      <div className="absolute inset-0 rounded-full overflow-hidden">
        <div 
          className="w-full h-full" 
          style={{ transform: `rotate(${valueToAngle(value)}deg)` }}
        >
          <div
            className={cn(
              "absolute", 
              indicatorSizeClasses[size],
              // If actualColor starts with "bg-", apply it as a Tailwind class
              actualColor.startsWith("bg-") ? actualColor : undefined
            )}
            style={{
              left: "50%",
              top: "0%",
              transform: "translateX(-50%)",
              // Otherwise, (if it's a CSS var, hex, rgb, etc.) apply it as an inline style
              backgroundColor: actualColor.startsWith("bg-") ? undefined : actualColor,
              borderRadius: "2px",
            }}
          />
        </div>
      </div>
    </div>
  );
});

KnobRenderer.displayName = "KnobRenderer";

// Main Knob component that handles drag logic
const Knob = React.forwardRef<HTMLDivElement, KnobProps>(
  ({
    min = 0,
    max = 100,
    value,
    defaultValue = 50,
    step = 1,
    speed = 1,
    acceleration = 1,
    size = "md",
    color = "hsl(var(--primary))",
    label,
    unit = "",
    startPosition = 7,
    endPosition = 5,
    hasStop = false,
    stopValue = 50,
    decimals = 2,
    enableDoubleClick = true,
    onChange,
    className,
    disabled = false,
    ...props
  }, ref) => {
    const [internalValue, setInternalValue] = useState(defaultValue)
    const currentValue = value !== undefined ? value : internalValue
    const dragContainerRef = useRef<HTMLDivElement>(null)
    const isAtStop = hasStop && currentValue === stopValue
    
    // Use refs to maintain state across renders
    const stateRef = useRef({
      isDragging: false,
      startY: 0,
      accumulator: 0,
      currentDragValue: currentValue,
      lastValue: currentValue,
      // Store props in ref to access latest values in event handlers
      // without causing effect to re-run
      props: {
        min,
        max,
        step,
        speed,
        acceleration,
        hasStop,
        disabled
      }
    });
    
    // Update ref when props change
    useEffect(() => {
      stateRef.current.props = {
        min,
        max,
        step,
        speed,
        acceleration,
        hasStop,
        disabled
      };
    }, [min, max, step, speed, acceleration, hasStop, disabled]);
    
    // Update ref when currentValue changes
    useEffect(() => {
      stateRef.current.lastValue = currentValue;
      if (!stateRef.current.isDragging) {
        stateRef.current.currentDragValue = currentValue;
      }
    }, [currentValue]);

    // Process a new value (apply step, clamp, etc.)
    const processValue = useCallback((newValue: number) => {
      const { min, max, step, hasStop } = stateRef.current.props;
      
      // Apply step
      newValue = Math.round(newValue / step) * step
      
      // Clamp to min/max
      newValue = Math.max(min, Math.min(max, newValue))
      
      // Remove the "stickiness" at minimum value to make it easier to drag from 0
      // Only apply stickiness when approaching the stop value
      if (hasStop && Math.abs(newValue - stopValue) < (max - min) * 0.02) {
        newValue = stopValue
      }
      
      return newValue
    }, [stopValue]);

    // Handle value changes
    const handleValueChange = useCallback((newValue: number) => {
      newValue = processValue(newValue)
      
      if (value === undefined) {
        setInternalValue(newValue)
      }
      
      if (onChange) {
        onChange(newValue)
      }
    }, [processValue, value, onChange]);

    // Setup drag handling once on mount and never re-create it
    useEffect(() => {
      if (!dragContainerRef.current) return;
      
      const container = dragContainerRef.current
      
      const onMouseDown = (e: MouseEvent) => {
        if (stateRef.current.props.disabled) return;
        
        e.preventDefault() // Prevent any default behavior
        
        const state = stateRef.current;
        state.isDragging = true;
        state.startY = e.clientY;
        state.accumulator = 0;
        state.currentDragValue = state.lastValue; // Start from the current value
        
        container.classList.add("cursor-grabbing");
        
        // Prevent text selection during drag
        document.body.style.userSelect = "none";
      }
      
      const onMouseMove = (e: MouseEvent) => {
        const state = stateRef.current;
        if (!state.isDragging) return;
        
        const { min, max, speed, acceleration } = state.props;
        
        const dy = e.clientY - state.startY;
        state.startY = e.clientY;
        
        // Calculate drag distance with acceleration
        state.accumulator += dy * speed;
        const dragDistance = state.accumulator * (1 + Math.abs(state.accumulator) * acceleration * 0.01);
        
        // Map drag to value change (negative dy = increase value)
        const valueRange = max - min;
        const valueDelta = -(dragDistance / 100) * valueRange;
        
        // Update our local drag value
        const previousValue = state.currentDragValue;
        state.currentDragValue = processValue(state.currentDragValue + valueDelta);
        
        // Reset accumulator after applying
        state.accumulator = 0;
        
        // Only call handleValueChange if the value has actually changed
        if (state.currentDragValue !== previousValue) {
          handleValueChange(state.currentDragValue);
        }
      }
      
      const onMouseUp = () => {
        const state = stateRef.current;
        if (!state.isDragging) return;
        
        state.isDragging = false;
        container.classList.remove("cursor-grabbing");
        
        // Restore text selection
        document.body.style.userSelect = "";
      }
      
      // Double-click handler to reset to default value
      const onDoubleClick = (e: MouseEvent) => {
        if (stateRef.current.props.disabled || !enableDoubleClick) return;
        
        e.preventDefault();
        handleValueChange(defaultValue);
      }
      
      container.addEventListener("mousedown", onMouseDown);
      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("mouseup", onMouseUp);
      
      // Add double-click event listener
      if (enableDoubleClick) {
        container.addEventListener("dblclick", onDoubleClick);
      }
      
      return () => {
        container.removeEventListener("mousedown", onMouseDown);
        document.removeEventListener("mousemove", onMouseMove);
        document.removeEventListener("mouseup", onMouseUp);
        
        // Clean up double-click listener
        if (enableDoubleClick) {
          container.removeEventListener("dblclick", onDoubleClick);
        }
      }
    }, [enableDoubleClick, defaultValue, handleValueChange]); // Add dependencies

    // Size classes
    const sizeClasses = {
      sm: "w-16 h-16",
      md: "w-24 h-24",
      lg: "w-32 h-32"
    }

    const fontSizeClasses = {
      sm: "text-xs",
      md: "text-sm",
      lg: "text-base"
    }

    return (
      <div 
        ref={ref} 
        className={cn("flex flex-col items-center", className)}
        {...props}
      >
        {label && (
          <div className={cn("font-medium mb-2", fontSizeClasses[size])}>{label}</div>
        )}
        {/* Drag container that surrounds the knob */}
        <div 
          ref={dragContainerRef}
          className={cn(
            "relative",
            disabled ? "opacity-50 cursor-not-allowed" : "cursor-grab",
            sizeClasses[size]
          )}
        >
          {/* Render the knob using the memoized component */}
          <KnobRenderer
            value={currentValue}
            min={min}
            max={max}
            size={size}
            color={color}
            hasStop={hasStop}
            startPosition={startPosition}
            endPosition={endPosition}
            stopValue={stopValue}
            isAtStop={isAtStop}
            disabled={disabled}
          />
        </div>
        
        {/* Value display */}
        <div className={cn("mt-2 text-center", fontSizeClasses[size])}>
          {Number.isInteger(currentValue) ? currentValue : currentValue.toFixed(decimals)}{unit}
        </div>
      </div>
    )
  }
)

Knob.displayName = "Knob"

export { Knob }
