import { RootStoreModel, RootStoreType } from './RootStore';
import { DeckStoreModel } from './DeckStore'; // To check deck properties and mock methods
import { db } from '../services/DatabaseService'; // Mocked DB service

// Mock the DatabaseService
jest.mock('../services/DatabaseService');

// Mock AudioAnalysisService and ExternalAnalysisService if their constructors are problematic for tests
jest.mock('../services/AudioAnalysisService', () => {
  return {
    AudioAnalysisService: jest.fn().mockImplementation(() => {
      return {
        startAnalysis: jest.fn().mockResolvedValue(undefined),
        // Add other methods if RootStore interacts with them during tests
      };
    }),
  };
});

jest.mock('../services/ExternalAnalysisService', () => {
  return {
    ExternalAnalysisService: jest.fn().mockImplementation(() => {
      return {
        // Add methods if RootStore interacts with them during tests
      };
    }),
  };
});


describe('RootStore', () => {
  let rootStore: RootStoreType;

  beforeEach(() => {
    // Clear all mock database data and mock function calls
    (db.clearAllMockData as jest.Mock)();
    (db.saveSetting as jest.Mock).mockClear();
    (db.getAllSettings as jest.Mock).mockClear();
    (db.saveDeckState as jest.Mock).mockClear();
    (db.loadDeckState as jest.Mock).mockClear();

    // Create a fresh RootStore instance
    // The mocked services will be used due to jest.mock calls above
    rootStore = RootStoreModel.create({});
    // Manually trigger hydration as it's normally called in App.tsx or afterCreate,
    // but for isolated store tests, we might need to control this.
    // For some tests, we'll call hydrateStores explicitly.
    // For others, we might want to test the state *before* hydration.
  });

  describe('updateDeckInstances', () => {
    it('Scenario 1: Increasing deck count', async () => {
      // Initial state (assuming default 1 deck after initial settings hydration if any, or 0 if no auto-hydration)
      // Let's ensure settingsStore reflects the desired initial state for this test.
      rootStore.settingsStore.numberOfDecks = 1; // Set initial desired state
      await rootStore.updateDeckInstances(1); // Sync to 1 deck
      expect(rootStore.decks.length).toBe(1);

      // Increase deck count
      rootStore.settingsStore.numberOfDecks = 2; // Update setting
      await rootStore.updateDeckInstances(2); // Call action

      expect(rootStore.decks.length).toBe(2);
      expect(rootStore.decks[0].id).toBe('deck-1');
      expect(rootStore.decks[1].id).toBe('deck-2');
      // Functionality check (e.g. new deck is loaded/initialized - stateLoaded becomes true after its own hydration)
      // For this test, we mainly care about instance creation.
      // We can assume DeckStore.afterCreate initializes basic state.
    });

    it('Scenario 2: Decreasing deck count', async () => {
      rootStore.settingsStore.numberOfDecks = 4;
      await rootStore.updateDeckInstances(4);
      expect(rootStore.decks.length).toBe(4);

      // Spy on beforeDestroy of a deck that will be removed
      const deckToRemove = rootStore.decks[3]; // deck-4
      const beforeDestroySpy = jest.spyOn(deckToRemove, 'beforeDestroy');

      rootStore.settingsStore.numberOfDecks = 2;
      await rootStore.updateDeckInstances(2);

      expect(rootStore.decks.length).toBe(2);
      expect(rootStore.decks.map(d => d.id)).toEqual(['deck-1', 'deck-2']);
      expect(beforeDestroySpy).toHaveBeenCalledTimes(1); // MST calls this automatically
      beforeDestroySpy.mockRestore();
    });
    
    it('Scenario 3: No change in deck count', async () => {
        rootStore.settingsStore.numberOfDecks = 2;
        await rootStore.updateDeckInstances(2);
        expect(rootStore.decks.length).toBe(2);
        const initialDeckInstances = rootStore.decks.map(d => d); // Store references

        await rootStore.updateDeckInstances(2); // Call again with same number
        expect(rootStore.decks.length).toBe(2);
        // Check that instances were not replaced
        expect(rootStore.decks[0]).toBe(initialDeckInstances[0]);
        expect(rootStore.decks[1]).toBe(initialDeckInstances[1]);
      });
  });

  describe('hydrateStores with multi-deck', () => {
    it('should call updateDeckInstances and loadDeckState for each deck', async () => {
      // Mock settingsStore.hydrateFromDb to set numberOfDecks
      // We'll simulate that settings have been loaded and numberOfDecks is 2
      (db.getAllSettings as jest.Mock).mockResolvedValueOnce([
        { key: 'numberOfDecks', value: 2 },
        // other settings can be added here if needed by other stores
      ]);

      // Mock loadDeckState for each deck that will be created
      (db.loadDeckState as jest.Mock)
        .mockResolvedValueOnce({ volume: 0.75, id: 'deck-1' }) // For deck-1
        .mockResolvedValueOnce({ volume: 0.85, id: 'deck-2' }); // For deck-2

      const updateDeckInstancesSpy = jest.spyOn(rootStore, 'updateDeckInstances');
      // We need to also spy on the DeckStore's loadDeckState if we want to assert it was called.
      // This is tricky because instances are created dynamically.
      // Instead, we'll check if db.loadDeckState was called, which is an indirect way.

      await rootStore.hydrateStores(); // This will call settingsStore.hydrateFromDb internally

      expect(rootStore.settingsStore.numberOfDecks).toBe(2);
      expect(updateDeckInstancesSpy).toHaveBeenCalledWith(2);
      expect(rootStore.decks.length).toBe(2);

      // Verify db.loadDeckState was called for each deck
      expect(db.loadDeckState).toHaveBeenCalledWith('deck-1');
      expect(db.loadDeckState).toHaveBeenCalledWith('deck-2');
      
      // Verify state was restored
      expect(rootStore.decks[0].volume).toBe(0.75);
      expect(rootStore.decks[1].volume).toBe(0.85);

      updateDeckInstancesSpy.mockRestore();
    });
  });

  describe('DeckStore state persistence with multiple decks', () => {
    it('should save and restore state for individual decks', async () => {
      // 1. Initial setup: hydrate with 2 decks
      (db.getAllSettings as jest.Mock).mockResolvedValueOnce([
        { key: 'numberOfDecks', value: 2 },
      ]);
      // Mock initial empty deck states
      (db.loadDeckState as jest.Mock)
        .mockResolvedValueOnce(null) // For deck-1
        .mockResolvedValueOnce(null); // For deck-2
      await rootStore.hydrateStores();
      expect(rootStore.decks.length).toBe(2);

      // 2. Modify state on deck-1 and save
      const deck1 = rootStore.decks[0];
      deck1.setVolume(0.9);
      deck1.setPitch(5);
      // await deck1.saveDeckState(); // saveDeckState is on RootStore or called internally by DeckStore actions
      // The DeckStore's actions like setVolume internally call saveDeckState.
      // We need to ensure that the db.saveDeckState mock is configured to work with this.
      // Let's assume setVolume calls db.saveDeckState via its flow.
      // We need to check if db.saveDeckState was called with the correct data.
      
      // Simulate the internal call to saveDeckState by setVolume
      // In a real test, if setVolume itself is a flow that calls saveDeckState,
      // that call to db.saveDeckState would be tested.
      // For this test, let's verify the state is in the store, then simulate a save.
      expect(deck1.volume).toBe(0.9);
      expect(deck1.pitch).toBe(5);
      
      // Manually trigger a save for deck-1 for test clarity
      // (as if an action on DeckStore called this)
      const deck1StateToSave = { volume: 0.9, pitch: 5, id: 'deck-1', /* other default serializable fields */ };
      // We need to get the snapshot of the deck to simulate realistic save
      // For simplicity, we'll mock the save directly here.
      await db.saveDeckState('deck-1', deck1StateToSave);


      // 3. Simulate app reload: Create new RootStore instance
      let newRootStore = RootStoreModel.create({});
      
      // 4. Hydrate new store: db.getAllSettings should still provide 2 decks
      (db.getAllSettings as jest.Mock).mockResolvedValueOnce([
        { key: 'numberOfDecks', value: 2 },
      ]);
      // db.loadDeckState should now return the saved state for deck-1
      (db.loadDeckState as jest.Mock)
        .mockImplementation(async (deckId: string) => {
          if (deckId === 'deck-1') return deck1StateToSave;
          if (deckId === 'deck-2') return null; // Deck 2 had no saved state
          return null;
        });

      await newRootStore.hydrateStores();

      // 5. Verify state restoration
      expect(newRootStore.decks.length).toBe(2);
      const restoredDeck1 = newRootStore.decks.find(d => d.id === 'deck-1');
      const restoredDeck2 = newRootStore.decks.find(d => d.id === 'deck-2');

      expect(restoredDeck1).toBeDefined();
      expect(restoredDeck2).toBeDefined();
      if (restoredDeck1) { // Type guard
        expect(restoredDeck1.volume).toBe(0.9);
        expect(restoredDeck1.pitch).toBe(5);
      }
      if (restoredDeck2) { // Type guard
        // Check default/initial state for deck-2 as it had no saved state
        expect(restoredDeck2.volume).toBe(DeckStoreModel.properties.volume.defaultValue); 
      }
    });
  });
});
