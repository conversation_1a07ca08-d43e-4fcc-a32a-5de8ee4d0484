import path from "path";
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [
    react(),
    tailwindcss(), // Add the plugin here
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"), // Add the alias
      "wavesurfer.js": path.resolve(__dirname, "./lib/wavesurfer.js/src"), // Add alias for wavesurfer.js source
    },
  },
});

