import React from 'react';
import { observer } from 'mobx-react-lite';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface TrackInfoDisplayProps {
  deck: DeckStoreInstance;
}

const TrackInfoDisplay: React.FC<TrackInfoDisplayProps> = observer(({ deck }) => {
  const track = deck.loadedTrack;

  if (!track) {
    return (
      <Card className="flex items-center justify-center">
        <CardContent>
          <p className="text-muted-foreground text-center">No track loaded</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="">
      <CardContent className="p-4">
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-bold truncate">{track.title || track.filename}</h3>
              <p className="text-sm text-muted-foreground truncate">{track.artist || 'Unknown Artist'}</p>
              <p className="text-xs text-muted-foreground truncate">{track.album || ''}</p>
            </div>
            <div className="flex flex-col items-end gap-1">
              <Badge variant="outline" className="font-mono">
                {deck.currentBpm.toFixed(1)} BPM
              </Badge>
              <span className="text-xs text-muted-foreground">
                {formatTime(deck.currentTime)} / {formatTime(deck.effectiveDuration.toFixed(2))}
              </span>
            </div>
          </div>
          
          <div className="flex justify-between mt-2">
            <span className="text-xs text-muted-foreground">
              {track.analysisStatus === "Analyzed" ? "Analysis Complete" : track.analysisStatus}
            </span>
            <span className="text-xs text-muted-foreground">
              {deck.isPlaying ? "Playing" : "Paused"}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

// Helper function to format time in MM:SS format
function formatTime(seconds: number): string {
  if (!seconds && seconds !== 0) return '--:--';
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

export default TrackInfoDisplay;

