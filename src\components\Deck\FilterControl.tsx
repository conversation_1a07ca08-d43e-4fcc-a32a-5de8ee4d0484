import React from 'react';
import { observer } from 'mobx-react-lite';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Knob } from '@/components/ui/knob';

interface FilterControlProps {
  deck: DeckStoreInstance;
}

const FilterControl: React.FC<FilterControlProps> = observer(({ deck }) => {
  const handleFilterChange = (value: number) => {
    // Update filter value in the deck store
    deck.setFilterValue(value);
  };
  
  return (
    <div className="flex flex-col items-center gap-4">
      <Knob
        min={0}
        max={70}
        speed={1}
        value={deck.filterValue}
        label="FILTER"
        unit="%"
        hasStop={true}
        startPosition={9}
        endPosition={3}
        stopValue={50}
        color="green"
        onChange={handleFilterChange}
        disabled={!deck.loadedTrack}
      />
    </div>
  );
});

export default FilterControl;
