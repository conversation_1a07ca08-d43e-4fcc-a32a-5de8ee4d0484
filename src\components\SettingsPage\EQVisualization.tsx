import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Slider } from '../ui/slider';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Play, Square } from 'lucide-react';

interface EQVisualizationProps {
  lowFreq: number;
  midFreq?: number;
  midLoFreq?: number;
  midHiFreq?: number;
  highFreq: number;
  lowGain: number;
  midGain?: number;
  midLoGain?: number;
  midHiGain?: number;
  highGain: number;
  is4BandMode: boolean;
  isFullKill: boolean;
  onFrequencyChange: (type: 'low' | 'mid' | 'midLo' | 'midHi' | 'high', value: number) => void;
}

const EQVisualization: React.FC<EQVisualizationProps> = observer(({
  lowFreq,
  midFreq,
  midLoFreq,
  midHiFreq,
  highFreq,
  lowGain,
  midGain,
  midLoGain,
  midHiGain,
  highGain,
  is4BandMode,
  isFullKill,
  onFrequencyChange
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
  const [sweepOscillator, setSweepOscillator] = useState<OscillatorNode | null>(null);
  const [lowFilter, setLowFilter] = useState<BiquadFilterNode | null>(null);
  const [midFilter, setMidFilter] = useState<BiquadFilterNode | null>(null);
  const [midLoFilter, setMidLoFilter] = useState<BiquadFilterNode | null>(null);
  const [midHiFilter, setMidHiFilter] = useState<BiquadFilterNode | null>(null);
  const [highFilter, setHighFilter] = useState<BiquadFilterNode | null>(null);
  const [gainNode, setGainNode] = useState<GainNode | null>(null);

  // Frequency range for visualization (logarithmic scale)
  const minFreq = 20;
  const maxFreq = 20000;
  const freqSteps = 200; // Number of points to calculate

  // Draw the frequency response graph
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set up dimensions
    const width = canvas.width;
    const height = canvas.height;
    const padding = { top: 20, right: 60, bottom: 40, left: 60 };
    const graphWidth = width - padding.left - padding.right;
    const graphHeight = height - padding.top - padding.bottom;

    // Draw background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
    ctx.fillRect(padding.left, padding.top, graphWidth, graphHeight);

    // Draw grid lines and labels for frequency (x-axis)
    ctx.strokeStyle = 'rgba(100, 100, 100, 0.2)';
    ctx.fillStyle = 'rgba(100, 100, 100, 0.8)';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';

    const freqLabels = [20, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 20000];
    freqLabels.forEach(freq => {
      const x = padding.left + graphWidth * (Math.log10(freq) - Math.log10(minFreq)) / (Math.log10(maxFreq) - Math.log10(minFreq));
      
      // Vertical grid line
      ctx.beginPath();
      ctx.moveTo(x, padding.top);
      ctx.lineTo(x, padding.top + graphHeight);
      ctx.stroke();
      
      // Frequency label
      let label = freq.toString();
      if (freq >= 1000) {
        label = `${freq / 1000}k`;
      }
      ctx.fillText(label, x, padding.top + graphHeight + 15);
    });

    // Draw grid lines and labels for gain (y-axis)
    const minGain = isFullKill ? -60 : -12; // Show -60dB for full kill mode to represent -∞
    const maxGain = 12;
    const gainStep = isFullKill ? 12 : 3;
    
    for (let gain = minGain; gain <= maxGain; gain += gainStep) {
      const y = padding.top + graphHeight * (1 - (gain - minGain) / (maxGain - minGain));
      
      // Horizontal grid line
      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(padding.left + graphWidth, y);
      ctx.stroke();
      
      // Gain label
      ctx.textAlign = 'right';
      ctx.fillText(`${gain} dB`, padding.left - 5, y + 3);
    }

    // Calculate and draw the frequency response curve
    ctx.strokeStyle = 'rgba(0, 120, 255, 0.8)';
    ctx.lineWidth = 2;
    ctx.beginPath();

    // Generate logarithmically spaced frequencies
    const frequencies: number[] = [];
    for (let i = 0; i < freqSteps; i++) {
      const t = i / (freqSteps - 1);
      const freq = minFreq * Math.pow(maxFreq / minFreq, t);
      frequencies.push(freq);
    }

    // Calculate the frequency response at each point
    frequencies.forEach((freq, i) => {
      // Calculate the gain at this frequency based on the EQ settings
      let totalGain = 0;

      // Low shelf filter
      if (freq <= lowFreq) {
        totalGain += lowGain;
      } else if (freq < lowFreq * 1.5) {
        // Transition region
        const t = (freq - lowFreq) / (lowFreq * 0.5);
        totalGain += lowGain * (1 - t);
      }

      if (is4BandMode) {
        // 4-band mode: midLo and midHi are peaking filters
        if (midLoFreq && freq > lowFreq && freq < midHiFreq!) {
          // Mid-Lo band (peaking filter)
          const distance = Math.abs(Math.log10(freq) - Math.log10(midLoFreq));
          const width = 0.5; // Q factor approximation
          const response = Math.exp(-distance * distance / (2 * width * width));
          totalGain += midLoGain! * response;
        }

        if (midHiFreq && freq >= midLoFreq! && freq < highFreq) {
          // Mid-Hi band (peaking filter)
          const distance = Math.abs(Math.log10(freq) - Math.log10(midHiFreq));
          const width = 0.5; // Q factor approximation
          const response = Math.exp(-distance * distance / (2 * width * width));
          totalGain += midHiGain! * response;
        }
      } else {
        // 3-band mode: mid is a peaking filter
        if (midFreq && freq > lowFreq && freq < highFreq) {
          // Mid band (peaking filter)
          const distance = Math.abs(Math.log10(freq) - Math.log10(midFreq));
          const width = 0.7; // Q factor approximation
          const response = Math.exp(-distance * distance / (2 * width * width));
          totalGain += midGain! * response;
        }
      }

      // High shelf filter
      if (freq >= highFreq) {
        totalGain += highGain;
      } else if (freq > highFreq * 0.7) {
        // Transition region
        const t = (freq - highFreq * 0.7) / (highFreq * 0.3);
        totalGain += highGain * t;
      }

      // Clamp the gain to the visible range
      totalGain = Math.max(minGain, Math.min(maxGain, totalGain));

      // Convert frequency and gain to canvas coordinates
      const x = padding.left + graphWidth * (Math.log10(freq) - Math.log10(minFreq)) / (Math.log10(maxFreq) - Math.log10(minFreq));
      const y = padding.top + graphHeight * (1 - (totalGain - minGain) / (maxGain - minGain));

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw frequency markers for the current EQ settings
    const drawFrequencyMarker = (freq: number, color: string, label: string) => {
      const x = padding.left + graphWidth * (Math.log10(freq) - Math.log10(minFreq)) / (Math.log10(maxFreq) - Math.log10(minFreq));
      
      // Draw vertical line at the frequency
      ctx.strokeStyle = color;
      ctx.lineWidth = 1;
      ctx.setLineDash([5, 3]);
      ctx.beginPath();
      ctx.moveTo(x, padding.top);
      ctx.lineTo(x, padding.top + graphHeight);
      ctx.stroke();
      ctx.setLineDash([]);
      
      // Draw label
      ctx.fillStyle = color;
      ctx.textAlign = 'center';
      ctx.fillText(label, x, padding.top - 5);
    };

    drawFrequencyMarker(lowFreq, 'rgba(255, 50, 50, 0.8)', 'Low');
    
    if (is4BandMode) {
      if (midLoFreq) drawFrequencyMarker(midLoFreq, 'rgba(255, 150, 0, 0.8)', 'Mid-Lo');
      if (midHiFreq) drawFrequencyMarker(midHiFreq, 'rgba(0, 200, 100, 0.8)', 'Mid-Hi');
    } else {
      if (midFreq) drawFrequencyMarker(midFreq, 'rgba(255, 200, 0, 0.8)', 'Mid');
    }
    
    drawFrequencyMarker(highFreq, 'rgba(50, 100, 255, 0.8)', 'High');

  }, [lowFreq, midFreq, midLoFreq, midHiFreq, highFreq, lowGain, midGain, midLoGain, midHiGain, highGain, is4BandMode, isFullKill]);

  // Initialize Web Audio API for the frequency sweep
  useEffect(() => {
    if (!audioContext) {
      const ctx = new AudioContext();
      setAudioContext(ctx);
      
      // Create filters
      const lowShelf = ctx.createBiquadFilter();
      lowShelf.type = 'lowshelf';
      setLowFilter(lowShelf);
      
      const midPeaking = ctx.createBiquadFilter();
      midPeaking.type = 'peaking';
      setMidFilter(midPeaking);
      
      const midLoPeaking = ctx.createBiquadFilter();
      midLoPeaking.type = 'peaking';
      setMidLoFilter(midLoPeaking);
      
      const midHiPeaking = ctx.createBiquadFilter();
      midHiPeaking.type = 'peaking';
      setMidHiFilter(midHiPeaking);
      
      const highShelf = ctx.createBiquadFilter();
      highShelf.type = 'highshelf';
      setHighFilter(highShelf);
      
      const gain = ctx.createGain();
      gain.gain.value = 0.2; // Lower volume for the test
      setGainNode(gain);
      
      // Connect the nodes
      if (is4BandMode) {
        // 4-band mode
        lowShelf.connect(midLoPeaking);
        midLoPeaking.connect(midHiPeaking);
        midHiPeaking.connect(highShelf);
      } else {
        // 3-band mode
        lowShelf.connect(midPeaking);
        midPeaking.connect(highShelf);
      }
      
      highShelf.connect(gain);
      gain.connect(ctx.destination);
    }
    
    return () => {
      // Clean up
      if (sweepOscillator) {
        sweepOscillator.stop();
        setSweepOscillator(null);
      }
    };
  }, [audioContext, is4BandMode, sweepOscillator]);

  // Update filter parameters when EQ settings change
  useEffect(() => {
    if (!lowFilter || !midFilter || !midLoFilter || !midHiFilter || !highFilter) return;
    
    // Set filter frequencies
    lowFilter.frequency.value = lowFreq;
    if (is4BandMode) {
      midLoFilter.frequency.value = midLoFreq || 350;
      midHiFilter.frequency.value = midHiFreq || 2500;
    } else {
      midFilter.frequency.value = midFreq || 1000;
    }
    highFilter.frequency.value = highFreq;
    
    // Set filter gains
    lowFilter.gain.value = isFullKill && lowGain <= -12 ? -40 : lowGain; // Approximate -Infinity with -40dB
    
    if (is4BandMode) {
      midLoFilter.gain.value = isFullKill && midLoGain! <= -12 ? -40 : midLoGain!;
      midHiFilter.gain.value = isFullKill && midHiGain! <= -12 ? -40 : midHiGain!;
    } else {
      midFilter.gain.value = isFullKill && midGain! <= -12 ? -40 : midGain!;
    }
    
    highFilter.gain.value = isFullKill && highGain <= -12 ? -40 : highGain;
    
  }, [lowFreq, midFreq, midLoFreq, midHiFreq, highFreq, lowGain, midGain, midLoGain, midHiGain, highGain, is4BandMode, isFullKill, lowFilter, midFilter, midLoFilter, midHiFilter, highFilter]);

  // Play a frequency sweep through the EQ
  const playFrequencySweep = () => {
    if (!audioContext || isPlaying) return;
    
    setIsPlaying(true);
    
    const osc = audioContext.createOscillator();
    osc.type = 'sine';
    
    // Connect to the first filter in the chain
    osc.connect(lowFilter!);
    
    // Sweep from 20Hz to 20kHz over 3 seconds
    const startTime = audioContext.currentTime;
    const endTime = startTime + 3;
    
    osc.frequency.setValueAtTime(20, startTime);
    osc.frequency.exponentialRampToValueAtTime(20000, endTime);
    
    osc.start(startTime);
    osc.stop(endTime);
    setSweepOscillator(osc);
    
    osc.onended = () => {
      setIsPlaying(false);
      setSweepOscillator(null);
    };
  };

  // Stop the frequency sweep
  const stopFrequencySweep = () => {
    if (sweepOscillator) {
      sweepOscillator.stop();
      setSweepOscillator(null);
      setIsPlaying(false);
    }
  };

  return (
    <div className="flex flex-col space-y-4">
      <div className="relative">
        <canvas 
          ref={canvasRef} 
          width={800} 
          height={400} 
          className="w-full h-auto border border-gray-200 rounded-md"
        />
      </div>
      
      <div className="flex justify-between items-center">
        <Button 
          onClick={isPlaying ? stopFrequencySweep : playFrequencySweep}
          variant={isPlaying ? "destructive" : "default"}
          className="flex items-center gap-2"
        >
          {isPlaying ? <><Square size={16} /> Stop Test</> : <><Play size={16} /> Test EQ</>}
        </Button>
        <div className="text-sm text-gray-500">
          {isPlaying ? "Playing frequency sweep (20Hz - 20kHz)..." : "Click to hear how the current EQ settings sound"}
        </div>
      </div>
    </div>
  );
});

export default EQVisualization;
