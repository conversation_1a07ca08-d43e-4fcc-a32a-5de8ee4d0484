import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/contexts/StoreContext';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { Button } from "@/components/ui/button";
import { Database } from "lucide-react";

// Import the actual child components
import DirectoryTreeView from './DirectoryTreeView';
import FileList from './FileList';
import FileDetails from './FileDetails';

const LibraryPage: React.FC = observer(() => {
  const { libraryStore, uiStore } = useStore();
  const [isCheckingExternalAnalysis, setIsCheckingExternalAnalysis] = useState(false);

  // Initialize state from UI store's persisted values
  const [selectedDirectoryPath, setSelectedDirectoryPath] = useState<string | null>(
    uiStore.selectedDirectoryPath
  );
  const [selectedFileId, setSelectedFileId] = useState<string | null>(
    uiStore.selectedFileId
  );

  const handleCheckAllExternalAnalysis = async () => {
    if (isCheckingExternalAnalysis) return;

    try {
      setIsCheckingExternalAnalysis(true);
      await libraryStore.checkExternalAnalysisForAllTracks();
    } catch (error) {
      console.error('Error checking external analysis for all tracks:', error);
    } finally {
      setIsCheckingExternalAnalysis(false);
    }
  };

  // Update UI store when selections change
  useEffect(() => {
    // Only update if the value has changed to avoid unnecessary saves
    if (selectedDirectoryPath !== uiStore.selectedDirectoryPath) {
      uiStore.setSelectedDirectoryPath(selectedDirectoryPath);
    }
  }, [selectedDirectoryPath, uiStore]);

  useEffect(() => {
    // Only update if the value has changed to avoid unnecessary saves
    if (selectedFileId !== uiStore.selectedFileId) {
      uiStore.setSelectedFileId(selectedFileId);
    }
  }, [selectedFileId, uiStore]);

  return (
    <div className="h-screen p-4 flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Library</h1>
        <Button
          variant="outline"
          size="sm"
          onClick={handleCheckAllExternalAnalysis}
          disabled={isCheckingExternalAnalysis}
          className="flex items-center gap-2"
        >
          <Database className="h-4 w-4" />
          {isCheckingExternalAnalysis ? 'Checking All Tracks...' : 'Check All External Analysis'}
        </Button>
      </div>
      <ResizablePanelGroup
        direction="horizontal"
        className="flex-grow rounded-lg border"
      >
        <ResizablePanel defaultSize={25} minSize={15} className="flex flex-col">
            <div className="p-2 font-semibold border-b">Folders</div>
            <div className="flex-grow overflow-auto">
              <DirectoryTreeView
                libraryStore={libraryStore}
                onSelectDirectory={setSelectedDirectoryPath}
              />
            </div>
         </ResizablePanel>
         <ResizableHandle withHandle />
        <ResizablePanel defaultSize={50} minSize={30} className="flex flex-col">
            <div className="p-2 font-semibold border-b">Files</div>
            <div className="flex-grow overflow-auto">
              <FileList
                selectedDirectoryPath={selectedDirectoryPath}
                libraryStore={libraryStore}
                selectedFileId={selectedFileId}
                onSelectFile={setSelectedFileId}
              />
            </div>
         </ResizablePanel>
         <ResizableHandle withHandle />
        <ResizablePanel defaultSize={25} minSize={15} className="flex flex-col">
            <div className="p-2 font-semibold border-b">Details</div>
            <div className="flex-grow overflow-auto">
              <FileDetails
                selectedFileId={selectedFileId}
                libraryStore={libraryStore}
              />
            </div>
         </ResizablePanel>
       </ResizablePanelGroup>
    </div>
  );
});

export default LibraryPage;
