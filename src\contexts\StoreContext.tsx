import React, { createContext, use<PERSON>ontext, ReactNode } from "react";
import rootStore, { RootStoreType } from "../stores"; // Import the instance and type

// Create the context with the root store instance
const StoreContext = createContext<RootStoreType>(rootStore);

// Define the provider component props
interface StoreProviderProps {
  children: ReactNode;
}

// Provider component (optional, but good practice if you might swap stores later)
export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  // You could potentially pass a different store instance here if needed
  return <StoreContext.Provider value={rootStore}>{children}</StoreContext.Provider>;
};

// Hook to use the store in components
export const useStore = (): RootStoreType => {
  const store = useContext(StoreContext);
  if (!store) {
    // This should technically never happen if the provider is set up correctly
    throw new Error("useStore must be used within a StoreProvider.");
  }
  return store;
};

// Export the context itself if needed elsewhere (less common)
export { StoreContext };
