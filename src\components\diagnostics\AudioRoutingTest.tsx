// src/components/AudioRoutingTest.tsx

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { RootStoreType } from '../../stores/RootStore';

export const AudioRoutingTest: React.FC = observer(() => {
  const rootStore = useStore() as RootStoreType;
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const initializeRouting = async () => {
    try {
      setError(null);
      addTestResult('Initializing audio routing manager...');
      
      await rootStore.audioRoutingManager.initialize();
      setIsInitialized(true);
      addTestResult('✅ Audio routing manager initialized successfully');
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMsg);
      addTestResult(`❌ Failed to initialize: ${errorMsg}`);
    }
  };

  const testDeckPath = async () => {
    try {
      addTestResult('Testing deck path creation...');
      
      // Create a test deck path
      const deckPath = await rootStore.audioRoutingManager.createDeckPath('test-deck');
      addTestResult('✅ Deck path created successfully');
      
      // Test volume control
      deckPath.setVolume(0.5);
      addTestResult('✅ Volume control working');
      
      // Test EQ
      deckPath.setEQ({ low: -3, mid: 2, high: -1 });
      addTestResult('✅ EQ control working');
      
      // Test crossfader
      deckPath.setCrossfaderGain(0.7);
      addTestResult('✅ Crossfader control working');
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      addTestResult(`❌ Deck path test failed: ${errorMsg}`);
    }
  };

  const testMasterPath = () => {
    try {
      addTestResult('Testing master path...');
      
      const masterPath = rootStore.audioRoutingManager.getMasterPath();
      masterPath.setMasterVolume(0.8);
      addTestResult('✅ Master volume control working');
      
      const analyzer = masterPath.getMasterAnalyzer();
      analyzer.startAnalysis();
      addTestResult('✅ Master analyzer started');
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      addTestResult(`❌ Master path test failed: ${errorMsg}`);
    }
  };

  const testHeadphonePath = () => {
    try {
      addTestResult('Testing headphone path...');
      
      const headphonePath = rootStore.audioRoutingManager.getHeadphonePath();
      headphonePath.setHeadphoneVolume(0.6);
      addTestResult('✅ Headphone volume control working');
      
      // Test deck monitoring
      rootStore.audioRoutingManager.enableHeadphoneMonitoring('deck-1');
      addTestResult('✅ Deck headphone monitoring enabled');
      
      rootStore.audioRoutingManager.disableHeadphoneMonitoring('deck-1');
      addTestResult('✅ Deck headphone monitoring disabled');
      
      // Test master monitoring
      rootStore.audioRoutingManager.enableMasterHeadphoneMonitoring();
      addTestResult('✅ Master headphone monitoring enabled');
      
      rootStore.audioRoutingManager.disableMasterHeadphoneMonitoring();
      addTestResult('✅ Master headphone monitoring disabled');
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      addTestResult(`❌ Headphone path test failed: ${errorMsg}`);
    }
  };

  const testEQModeSwitch = () => {
    try {
      addTestResult('Testing EQ mode switching...');
      
      rootStore.audioRoutingManager.updateEQMode();
      addTestResult('✅ EQ mode update working');
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      addTestResult(`❌ EQ mode test failed: ${errorMsg}`);
    }
  };

  const runAllTests = async () => {
    setTestResults([]);
    addTestResult('Starting audio routing system tests...');
    
    if (!isInitialized) {
      await initializeRouting();
    }
    
    if (isInitialized) {
      await testDeckPath();
      testMasterPath();
      testHeadphonePath();
      testEQModeSwitch();
      addTestResult('🎉 All tests completed!');
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setError(null);
  };

  const disposeRouting = () => {
    try {
      rootStore.audioRoutingManager.dispose();
      setIsInitialized(false);
      addTestResult('✅ Audio routing manager disposed');
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      addTestResult(`❌ Failed to dispose: ${errorMsg}`);
    }
  };

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <div className="space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Audio Routing System Test</h2>
          <p className="text-muted-foreground">
            Test the new audio routing system functionality
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}

        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={initializeRouting} 
            disabled={isInitialized}
            variant={isInitialized ? "secondary" : "default"}
          >
            {isInitialized ? "✅ Initialized" : "Initialize Routing"}
          </Button>
          
          <Button 
            onClick={runAllTests} 
            disabled={!isInitialized}
            variant="outline"
          >
            Run All Tests
          </Button>
          
          <Button 
            onClick={clearResults} 
            variant="outline"
            size="sm"
          >
            Clear Results
          </Button>
          
          <Button 
            onClick={disposeRouting} 
            disabled={!isInitialized}
            variant="destructive"
            size="sm"
          >
            Dispose
          </Button>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Test Results:</Label>
          <div className="bg-gray-50 border rounded p-3 h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-muted-foreground text-sm">No test results yet...</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>
            This test component verifies that the audio routing system is working correctly.
            It tests deck paths, master path, headphone monitoring, and EQ mode switching.
          </p>
        </div>
      </div>
    </Card>
  );
});

export default AudioRoutingTest;
