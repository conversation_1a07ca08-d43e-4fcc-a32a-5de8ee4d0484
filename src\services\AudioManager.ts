// src/services/AudioManager.ts

import { audioPermissionManager } from './AudioPermissionManager';

/**
 * Manages the shared AudioContext instance for the application.
 * Using a shared context is generally recommended for performance
 * and resource management.
 */

let sharedAudioContext: AudioContext | null = null;

/**
 * Retrieves or creates the shared AudioContext instance.
 * Only creates the context if audio permission has been granted.
 * @returns The shared AudioContext instance.
 * @throws Error if audio permission has not been granted
 */
export function getSharedAudioContext(): AudioContext {
    // Check if audio permission has been granted
    if (!audioPermissionManager.isPermissionGranted()) {
        throw new Error("Audio permission not granted. Cannot create AudioContext without user interaction.");
    }

    if (!sharedAudioContext) {
        try {
            sharedAudioContext = new AudioContext();
            console.log("Shared AudioContext created successfully.");
            // Optional: Handle state changes (e.g., suspended context)
            sharedAudioContext.onstatechange = () => {
                console.log(`Shared AudioContext state changed to: ${sharedAudioContext?.state}`);
            };
        } catch (error) {
            console.error("Failed to create shared AudioContext:", error);
            // Handle the error appropriately - maybe throw, or return a dummy context?
            // For now, re-throwing might be best to signal a critical failure.
            throw new Error("Could not create AudioContext. Web Audio API might not be supported or enabled.");
        }
    }
    // Optional: Resume context if it's suspended (e.g., due to browser policy)
    if (sharedAudioContext.state === 'suspended') {
        sharedAudioContext.resume().catch(err => console.error("Failed to resume suspended AudioContext:", err));
    }
    return sharedAudioContext;
}

/**
 * Checks if the shared AudioContext is available (permission granted and context created)
 * @returns true if AudioContext is available, false otherwise
 */
export function isAudioContextAvailable(): boolean {
    return audioPermissionManager.isPermissionGranted() && sharedAudioContext !== null;
}

/**
 * Safely gets the shared AudioContext if available, returns null otherwise
 * @returns The shared AudioContext instance or null if not available
 */
export function getSharedAudioContextSafe(): AudioContext | null {
    if (!audioPermissionManager.isPermissionGranted()) {
        return null;
    }

    try {
        return getSharedAudioContext();
    } catch (error) {
        console.error("Failed to get shared AudioContext:", error);
        return null;
    }
}

/**
 * Closes the shared AudioContext if it exists.
 * Useful for cleanup during application shutdown or testing.
 */
export function closeSharedAudioContext(): void {
    if (sharedAudioContext) {
        sharedAudioContext.close()
            .then(() => {
                console.log("Shared AudioContext closed.");
                sharedAudioContext = null;
            })
            .catch(err => console.error("Error closing shared AudioContext:", err));
    }
}
