# CONTRIBUTING to wavesurfer.js

Hello there,

Firstly, a heartfelt thank you! We sincerely appreciate your interest in wavesurfer.js and are really excited to see your contributions to our community.

Here are a few guidelines to keep in mind when you're ready to contribute:

## 1. Search in Existing Issues

Before submitting a new issue, we kindly ask you to take a moment to search through our [existing issues](https://github.com/katspaugh/wavesurfer.js/issues?q=is%3Aissue). There's a chance that someone has already raised the point you're interested in. This step helps to keep our issues page clean and productive.

## 2. Questions and Feature Requests

Got a burning question or a brilliant feature idea? That's fantastic! But instead of the issues section, we ask you to post these in our [Discussions](https://github.com/katspaugh/wavesurfer.js/discussions/categories/ideas) forum. This helps to separate enhancement ideas and questions from the bugs and issues which need immediate attention from the developers.

To visit the forum, [click here](https://github.com/katspaugh/wavesurfer.js/discussions).

## 3. Reporting Bugs

Stumbled upon a bug? Sorry about that! We're constantly working to improve wavesurfer.js and your bug reports help us do just that.

When you post a bug report, please include the necessary code that will help us reproduce the bug. The more details you provide, the quicker we can get to the root of the problem and resolve it.

By following these guidelines, you're helping us maintain a productive, organized community. We can't wait to see your contributions to wavesurfer.js. Thank you again for your help!
