/**
 * Converts standard musical key notation to Camelot notation
 * @param standardKey - Key in standard notation (e.g., "C", "Am")
 * @returns Camelot notation (e.g., "8B", "8A") or null if conversion fails
 */
export function convertToCamelot(standardKey: string): string | null {
  // Mapping of standard keys to Camelot notation
  const keyMap: Record<string, string> = {
    // Major keys (B notation in Camelot)
    'C': '8B', 'G': '9B', 'D': '10B', 'A': '11B', 'E': '12B', 
    'B': '1B', 'F#': '2B', 'Gb': '2B', 'Db': '3B', 'C#': '3B', 
    'Ab': '4B', 'G#': '4B', 'Eb': '5B', 'D#': '5B', 'Bb': '6B', 'A#': '6B', 'F': '7B',
    
    // Minor keys (A notation in Camelot)
    'Am': '8A', 'Em': '9A', 'Bm': '10A', 'F#m': '11A', 'Gbm': '11A', 'C#m': '12A', 'Dbm': '12A',
    'G#m': '1A', 'Abm': '1A', 'D#m': '2A', 'Ebm': '2A', 'A#m': '3A', 'Bbm': '3A', 
    'Fm': '4A', 'Cm': '5A', 'Gm': '6A', 'Dm': '7A'
  };

  return keyMap[standardKey] || null;
}