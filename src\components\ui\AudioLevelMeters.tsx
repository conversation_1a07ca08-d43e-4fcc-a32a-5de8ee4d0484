import React, { useEffect, useState } from "react";
import { Label } from "../ui/label";
import { cn } from "../../lib/utils";

interface AudioLevelMetersProps {
  analyzer: { getRMS: () => number; getPeak: () => number } | null;
  horizontal?: boolean; // If true, meters are horizontal (default), else vertical
}

const AudioLevelMeters: React.FC<AudioLevelMetersProps> = ({
  analyzer,
  horizontal = true,
}) => {
  const [rmsLevel, setRmsLevel] = useState(0);
  const [peakLevel, setPeakLevel] = useState(0);

  useEffect(() => {
    let animationFrameId: number;
    const updateLevels = () => {
      if (analyzer) {
        setRmsLevel(analyzer.getRMS());
        setPeakLevel(analyzer.getPeak());
      }
      animationFrameId = requestAnimationFrame(updateLevels);
    };
    updateLevels();
    return () => {
      if (animationFrameId) cancelAnimationFrame(animationFrameId);
    };
  }, [analyzer]);

  // Convert linear values to dB for display
  const rmsLeveldB = rmsLevel > 0 ? 20 * Math.log10(rmsLevel) : -Infinity;
  const peakLeveldB = peakLevel > 0 ? 20 * Math.log10(peakLevel) : -Infinity;

  // Calculate bar fill (0-100%)
  const rmsPercent = Math.max(0, Math.min(100, ((rmsLeveldB + 60) / 60) * 100));
  const peakPercent = Math.max(
    0,
    Math.min(100, ((peakLeveldB + 60) / 60) * 100),
  );

  // Determine level colors
  const getLevelColor = (dB: number) => {
    if (dB > -6) return "bg-red-500";
    if (dB > -12) return "bg-yellow-500";
    if (dB > -24) return "bg-green-500";
    return "bg-green-300";
  };

  if (horizontal) {
    return (
      <div className="space-y-2">
        <Label className="text-sm font-medium">Audio Levels</Label>
        <div className="flex items-center space-x-2">
          {/* RMS Level Meter */}
          <div className="flex-1">
            <div className="text-xs text-muted-foreground mb-1">RMS</div>
            <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={cn(
                  "h-full transition-all duration-75",
                  getLevelColor(rmsLeveldB),
                )}
                style={{ width: `${rmsPercent}%` }}
              />
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {isFinite(rmsLeveldB) ? `${rmsLeveldB.toFixed(1)} dB` : "-∞ dB"}
            </div>
          </div>
          {/* Peak Level Meter */}
          <div className="flex-1">
            <div className="text-xs text-muted-foreground mb-1">Peak</div>
            <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={cn(
                  "h-full transition-all duration-75",
                  getLevelColor(peakLeveldB),
                )}
                style={{ width: `${peakPercent}%` }}
              />
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {isFinite(peakLeveldB) ? `${peakLeveldB.toFixed(1)} dB` : "-∞ dB"}
            </div>
          </div>
        </div>
        {/* Level Scale Reference */}
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>-60dB</span>
          <span>-24dB</span>
          <span>-12dB</span>
          <span>-6dB</span>
          <span>0dB</span>
        </div>
        {/* Clipping Warning */}
        {peakLeveldB > -3 && (
          <div className="text-xs text-red-500 bg-red-50 p-2 rounded text-center">
            ⚠️ Audio clipping detected! Reduce master volume.
          </div>
        )}
      </div>
    );
  } else {
    // Vertical meters (for deck fader panel)
    return (
      <div className="flex flex-col items-center justify-end h-48 w-6">
        {/* RMS Meter */}
        <div className="relative w-3 h-full bg-gray-200 rounded-full overflow-hidden mb-1">
          <div
            className={`absolute bottom-0 left-0 w-full transition-all duration-75 ${getLevelColor(rmsLeveldB)}`}
            style={{ height: `${rmsPercent}%` }}
          />
        </div>
        <div className="text-[10px] text-muted-foreground">
          {isFinite(rmsLeveldB) ? `${rmsLeveldB.toFixed(1)} dB` : "-∞ dB"}
        </div>
        {/* Peak Meter */}
        <div className="relative w-3 h-full bg-gray-200 rounded-full overflow-hidden mt-1">
          <div
            className={`absolute bottom-0 left-0 w-full transition-all duration-75 ${getLevelColor(peakLeveldB)}`}
            style={{ height: `${peakPercent}%` }}
          />
        </div>
        <div className="text-[10px] text-muted-foreground">
          {isFinite(peakLeveldB) ? `${peakLeveldB.toFixed(1)} dB` : "-∞ dB"}
        </div>
      </div>
    );
  }
};

export default AudioLevelMeters;
