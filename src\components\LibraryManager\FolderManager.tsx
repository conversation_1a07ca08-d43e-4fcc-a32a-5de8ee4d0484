import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/contexts/StoreContext'; // Import useStore
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
// Keep StoredDirectoryHandle type if needed for props/internal types, otherwise remove.
// For now, we'll keep it as it might be useful for the list item type implicitly.
import { StoredDirectoryHandle } from '@/services/DatabaseService';

const FolderManager: React.FC = observer(() => { // Wrap component with observer
  const { libraryStore } = useStore(); // Get the library store instance
  const trackedDirs = libraryStore.trackedDirectoriesList; // Get tracked dirs from store view
  const isScanning = libraryStore.scanStatus === 'scanning'; // Check scan status

  const handleAddFolder = async () => {
    try {
      // Check for browser support
      if (!window.showDirectoryPicker) {
          toast.error("Your browser does not support the File System Access API needed to add folders.");
          return;
      }

      const dirHandle = await window.showDirectoryPicker({
          mode: 'readwrite' // Request readwrite permission for persistence
      });

      // Call the store action to handle adding the directory and triggering the scan
      await libraryStore.addTrackedDirectory(dirHandle);
      // Toast notifications are handled within the store action now

    } catch (error: any) {
      if (error.name === 'AbortError') {
        // User cancelled the picker
        toast.info("Folder selection cancelled.");
      } else {
        console.error("Error adding folder:", error);
        toast.error("Failed to add folder. See console for details.");
        // Consider specific permission errors
        if (error.name === 'NotAllowedError') {
            toast.error("Permission denied to access the selected folder.");
        }
      }
    }
  };

  const handleRemoveFolder = async (id: string, name: string) => {
    // Confirmation and logic are now handled within the store action
    // The store action already includes a confirm() check
    await libraryStore.removeTrackedDirectory(id);
    // Toast notifications are also handled within the store action
  };

  const handleRescanAll = () => {
      // Call the store action to trigger a full scan
      libraryStore.scanAllFolders();
      // Toast notification is handled within the store action
  };

  return (
    <div className="p-4 border rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-3">Manage Music Folders</h2>
      <div className="flex space-x-2 mb-4">
          {/* Disable buttons when scanning */}
          <Button onClick={handleAddFolder} disabled={isScanning}>Add Folder</Button>
          <Button onClick={handleRescanAll} variant="outline" disabled={isScanning}>Rescan All</Button>
      </div>

      {/* Remove isLoading checks, directly show content based on trackedDirs */}
      {trackedDirs.length === 0 && (
          <p className="text-sm text-muted-foreground">No folders added yet. Click "Add Folder" to start.</p>
      )}

      {trackedDirs.length > 0 && (
        <ScrollArea className="h-48 border rounded-md p-2">
          <ul>
            {trackedDirs.map((dir) => (
              <li key={dir.id} className="flex justify-between items-center p-2 hover:bg-accent rounded">
                <span className="text-sm font-medium truncate" title={dir.name}>{dir.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-red-500 hover:text-red-700"
                  onClick={() => handleRemoveFolder(dir.id, dir.name)}
                  disabled={isScanning} // Disable remove button during scan
                >
                  Remove
                </Button>
              </li>
            ))}
          </ul>
        </ScrollArea>
      )}
    </div>
  );
}); // Ensure observer HOC closes correctly

export default FolderManager;
