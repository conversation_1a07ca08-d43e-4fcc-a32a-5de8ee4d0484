name: Lint
on: [pull_request]

jobs:
  eslint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/yarn

      - name: Run lint
        run: npm run lint:report
        continue-on-error: true

      - name: Annotate code with linting results
        uses: ataylorme/eslint-annotate-action@v3
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          report-json: "eslint_report.json"
