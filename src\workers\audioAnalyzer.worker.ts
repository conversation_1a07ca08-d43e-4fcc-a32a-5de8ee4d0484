import * as Essentia from "essentia.js";

// Define the structure for the analysis results
interface AnalysisResults {
  bpm?: number; // Single BPM value from Essentia
  key?: string;
  danceability?: number;
  firstBeatTime?: number; // Time of the first detected beat in seconds
  beatGridTimes?: number[]; // Array of beat timestamps in seconds
  // Add other fields as needed: happiness, aggressiveness, segments, length etc.
  error?: string;
}

// Define the structure for messages received by the worker (matches WorkerPostMessageData in service)
interface WorkerData {
  action: "analyze"; // Explicitly define action for analyze messages
  audioData: Float32Array;
  sampleRate: number;
  trackId: string;
}

// Define the structure for initialization message
interface InitMessage {
  action: "init";
}

// Define the structure for messages received by the worker (union of possible message types)
type ReceivedMessage = WorkerData | InitMessage;

let essentiaInstance: any = null; // Use any for now due to basic .d.ts
let beatDetectorWorker: Worker | null = null;

interface BeatDetectorResultMessage {
  trackId: string;
  peaks?: any[]; // Define more specific type if needed
  offset?: number;
  firstBar?: number;
  bpm?: number;
  error?: string;
}

/**
 * Preprocesses audio data for analysis.
 * - Downsamples to a target sample rate if higher.
 * @param audioData - The input audio data (Float32Array).
 * @param sampleRate - The original sample rate.
 * @param targetSampleRate - The desired sample rate after downsampling.
 * @returns An object containing the preprocessed audio data and the new sample rate.
 */
function preprocessAudio(
  audioData: Float32Array,
  sampleRate: number,
  targetSampleRate: number = 16000,
): { audioData: Float32Array; sampleRate: number } {
  let processedAudio = audioData;
  let processedSampleRate = sampleRate;

  // Downsampling
  if (sampleRate > targetSampleRate) {
    const ratio = sampleRate / targetSampleRate;
    const newLength = Math.round(audioData.length / ratio);
    const downsampledData = new Float32Array(newLength);

    for (let i = 0; i < newLength; i++) {
      // Simple linear interpolation for downsampling
      const originalIndex = i * ratio;
      const index1 = Math.floor(originalIndex);
      const index2 = Math.min(Math.ceil(originalIndex), audioData.length - 1);
      const fraction = originalIndex - index1;

      downsampledData[i] =
        audioData[index1] * (1 - fraction) + audioData[index2] * fraction;
    }

    processedAudio = downsampledData;
    processedSampleRate = targetSampleRate;
    console.log(
      `Audio Analyzer Worker: Downsampled audio from ${sampleRate}Hz to ${processedSampleRate}Hz. New length: ${processedAudio.length}`,
    );
  } else {
    console.log(
      `Audio Analyzer Worker: Sample rate ${sampleRate}Hz is at or below target ${targetSampleRate}Hz. No downsampling needed.`,
    );
  }

  return { audioData: processedAudio, sampleRate: processedSampleRate };
}

// --- Main Message Handler ---
self.onmessage = async (event: MessageEvent<ReceivedMessage>) => {
  const data = event.data;

  if (data.action === "init") {
    console.log(
      "Audio Analyzer Worker: Received init message. Initializing Essentia.js...",
    );
    try {
      // Initialize Essentia.js with the WASM module
      // The code below is the correct new way to initialize webasm
      const essentiaWasm = Essentia.EssentiaWASM;
      essentiaInstance = new Essentia.Essentia(essentiaWasm.EssentiaWASM);

      console.log(
        "Audio Analyzer Worker: Essentia.js Initialized successfully.",
      );

      // Initialize the new BeatDetector worker
      try {
        beatDetectorWorker = new Worker(
          new URL("./beatDetector.worker.ts", import.meta.url),
          { type: "module" },
        );
        beatDetectorWorker.onmessage = (
          e: MessageEvent<
            BeatDetectorResultMessage | { status: string; worker: string }
          >,
        ) => {
          if (
            "status" in e.data &&
            e.data.status === "ready" &&
            e.data.worker === "BeatDetectorWorker"
          ) {
            console.log(
              "Audio Analyzer Worker: BeatDetector.worker reported ready.",
            );
          } else if ("trackId" in e.data) {
            console.log(
              `Audio Analyzer Worker: Received results from BeatDetector.worker for track ${e.data.trackId}:`,
              e.data,
            );
          }
        };
        beatDetectorWorker.onerror = (err) => {
          console.error(
            "Audio Analyzer Worker: Error from BeatDetector.worker:",
            err,
          );
        };
        beatDetectorWorker.postMessage({ action: "init" }); // Initialize BeatDetector worker
      } catch (workerError) {
        console.error(
          "Audio Analyzer Worker: Failed to initialize BeatDetector.worker:",
          workerError,
        );
      }
      console.log("Essentia.js version:", essentiaInstance.version);
      console.log(
        "Essentia.js available algorithms:",
        essentiaInstance.algorithmNames,
      );

      // Inform main thread that worker is ready
      self.postMessage({ status: "ready" });
    } catch (error: any) {
      console.error("Audio Analyzer Worker: Failed to initialize:", error);
      self.postMessage({
        error: `Worker initialization failed: ${error.message || error}`,
      });
    }
  } else if (data.action === "analyze") {
    if (!essentiaInstance) {
      console.error(
        "Audio Analyzer Worker: Received analyze message before initialization.",
      );
      self.postMessage({
        trackId: (data as WorkerData).trackId || "unknown",
        error: "Worker not initialized.",
      });
      return;
    }

    const { audioData, sampleRate, trackId } = data as WorkerData;

    if (!audioData || !sampleRate || !trackId) {
      console.error(
        "Audio Analyzer Worker: Invalid analyze message received.",
        data,
      );
      self.postMessage({
        trackId: trackId || "unknown",
        error: "Invalid analyze message received by worker.",
      });
      return;
    }

    console.log(
      `Audio Analyzer Worker: Received analysis request for TrackID: ${trackId}, SampleRate: ${sampleRate}, DataLength: ${audioData.length}`,
    );

    // Also send data to the new beatDetector.worker.ts
    if (beatDetectorWorker) {
      // Send a copy of audioData if it might be modified by preprocessing for Essentia
      // Or ensure preprocessing happens after this postMessage
      beatDetectorWorker.postMessage({
        action: "analyze",
        audioData: audioData.slice(), // Send a copy
        sampleRate: sampleRate,
        trackId: trackId,
      });

      // wait 1 millisecond to allow the beatDetectorWorker to start
      await new Promise((resolve) => setTimeout(resolve, 1));
    }

    let results: AnalysisResults = {};
    let audioVector: any = null; // Use any type due to basic essentia.js declaration file

    try {
      // Preprocess audio data
      const preprocessedData = preprocessAudio(audioData, sampleRate);
      const processedAudioData = preprocessedData.audioData;
      const processedSampleRate = preprocessedData.sampleRate;

      // 1. Convert received Float32Array to Essentia Vector
      console.log(
        `Audio Analyzer Worker: Converting audio data to Essentia vector for track ${trackId}`,
      );
      audioVector = essentiaInstance.arrayToVector(processedAudioData);
      console.log(
        `Audio Analyzer Worker: Essentia vector created, size: ${audioVector.size()}`,
      );

      console.log(
        `Audio Analyzer Worker: Starting analysis for track ${trackId}`,
      );

      // 2. Run Essentia Algorithms
      // --- BPM and Beat Detection (Essentia.js) ---
      try {
        console.time("Essentia.js Rhythm Analysis");

        // 1. Get BPM using PercivalBpmEstimator
        let rhythmResult = essentiaInstance.PercivalBpmEstimator(
          audioVector,
          1024,
          2048,
          128,
          128,
          200,
          75,
          processedSampleRate,
        );
        results.bpm = rhythmResult.bpm;
        console.log(
          `Audio Analyzer Worker: Essentia.js BPM Analysis complete for track ${trackId}: ${results.bpm}`,
        );

        // 2. Get beat positions using RhythmExtractor
        console.log(
          `Audio Analyzer Worker: Detecting beat positions for track ${trackId}`,
        );
        const rhythmExtractorResult = essentiaInstance.RhythmExtractor2013(
          audioVector,
          200, // maxTempo
          "multifeature", // method
          75, // minTempo
        );

        // Convert beat positions to seconds
        const beatPositions = essentiaInstance.vectorToArray(
          rhythmExtractorResult.ticks,
        );

        if (beatPositions && beatPositions.length > 0) {
          // Store first beat time
          results.firstBeatTime = beatPositions[0];
          if (
            results.firstBeatTime === undefined ||
            isNaN(results.firstBeatTime)
          ) {
            results.firstBeatTime = 0;
          }

          // Store all beat times for beat grid
          results.beatGridTimes = beatPositions;

          console.log(
            `Audio Analyzer Worker: Found ${beatPositions.length} beats, first beat at ${results.firstBeatTime.toFixed(3)}s`,
          );
        } else {
          console.warn(
            `Audio Analyzer Worker: No beats detected for track ${trackId}`,
          );
        }

        // Clean up
        rhythmResult = null;
        console.timeEnd("Essentia.js Rhythm Analysis");
      } catch (rhythmError) {
        console.error(
          `Audio Analyzer Worker: Rhythm analysis failed for track ${trackId}:`,
          rhythmError,
        );
        // Try fallback method if the main method fails
        try {
          console.log(
            `Audio Analyzer Worker: Trying fallback beat detection for track ${trackId}`,
          );

          // Fallback method using BeatTrackerMultiFeature
          const beatTrackerResult = essentiaInstance.BeatTrackerMultiFeature(
            audioVector,
            processedSampleRate,
          );

          const beatPositions = essentiaInstance.vectorToArray(
            beatTrackerResult.ticks,
          );

          if (beatPositions && beatPositions.length > 0) {
            results.firstBeatTime = beatPositions[0];
            if (
              results.firstBeatTime === undefined ||
              isNaN(results.firstBeatTime)
            ) {
              results.firstBeatTime = 0;
            }
            results.beatGridTimes = beatPositions;
            console.log(
              `Audio Analyzer Worker: Fallback method found ${beatPositions.length} beats, first beat at ${results.firstBeatTime.toFixed(3)}s`,
            );
          } else {
            console.warn(
              `Audio Analyzer Worker: Fallback beat detection found no beats for track ${trackId}`,
            );
          }
        } catch (fallbackError) {
          console.error(
            `Audio Analyzer Worker: Fallback beat detection also failed for track ${trackId}:`,
            fallbackError,
          );
        }
      }

      // --- Key ---
      try {
        console.time("KeyExtractor");
        // Use TonalExtractor for more comprehensive key analysis
        const keyResult = essentiaInstance.KeyExtractor(
          audioVector,
          true,
          4096,
          4096,
          12,
          3500,
          60,
          25,
          0.2,
          "bgate",
          processedSampleRate,
        );
        // const tonalResult = essentiaInstance.TonalExtractor(audioVector, processedSampleRate);
        console.timeEnd("KeyExtractor");
        // Consider key strength for more reliable results
        if (keyResult.strength < 0.3) {
          console.log(
            `Audio Analyzer Worker: Low key strength (${keyResult.strength.toFixed(2)}) for track ${trackId}. Key might be unreliable.`,
          );
        }
        console.log(
          `Audio Analyzer Worker: KeyExtractor completed for track ${trackId}`,
        );
        results.key = keyResult.key + (keyResult.scale === "minor" ? "m" : "");
        console.log(
          `Audio Analyzer Worker: Key Analysis complete for track ${trackId}: ${keyResult.key} ${keyResult.scale} = ${results.key}`,
        );
      } catch (keyError) {
        console.error(
          `Audio Analyzer Worker: Key analysis failed for track ${trackId}:`,
          keyError,
        );
      }

      // --- Danceability ---
      console.log(
        `Audio Analyzer Worker: Danceability Analysis (SKIPPED - requires more features) complete for track ${trackId}`,
      );

      // --- Segmentation (Example using NoveltyCurve - Placeholder) ---
      console.log(
        `Audio Analyzer Worker: Segmentation Analysis (SKIPPED - complex placeholder) complete for track ${trackId}`,
      );

      // --- Other Analyses (Mood, Genre, etc. - Placeholders) ---

      console.log(
        `Audio Analyzer Worker: Analysis finished successfully for track ${trackId}. Results:`,
        results,
      );
      // 3. Post Results Back (Success)
      console.log(
        `Audio Analyzer Worker: Sending successful results for track ${trackId}`,
        results,
      );
      self.postMessage({ trackId, results });
    } catch (error: any) {
      console.error(
        `Audio Analyzer Worker: Error analyzing track ${trackId}:`,
        error,
      );
      const errorMessage = error.message || "Unknown analysis error in worker";
      // 3. Post Results Back (Error)
      console.log(
        `Audio Analyzer Worker: Sending error result for track ${trackId}: ${errorMessage}`,
      );
      self.postMessage({ trackId, error: errorMessage });
    } finally {
      // 4. Clean up Essentia resources and preprocessed data
      if (audioVector) {
        try {
          // Don't use essentiaInstance.delete() as it would delete the entire instance
          // Instead, let JavaScript's garbage collection handle the cleanup
          audioVector = null; // Just clear the reference to help garbage collection
          console.log(
            `Audio Analyzer Worker: Released audio vector reference for track ${trackId}`,
          );
        } catch (deleteError) {
          console.error(
            `Audio Analyzer Worker: Error releasing audio vector for track ${trackId}:`,
            deleteError,
          );
        }
      }
      // Note: preprocessedData is a new Float32Array created in preprocessAudio.
      // JavaScript's garbage collection handles Float32Arrays, no explicit delete needed here.

      // Ensure other intermediate vectors created within try blocks are also deleted if necessary.
    }
  } else {
    console.warn(
      "Audio Analyzer Worker: Received unknown message action:",
      (data as any).action,
    );
  }
};

export {}; // Make this a module
