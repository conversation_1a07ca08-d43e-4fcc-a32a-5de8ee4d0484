import { types, flow, Instance } from "mobx-state-tree";
import { db } from "../services/DatabaseService";

export const UiStoreModel = types
  .model("UiStore", {
    // Basic UI state
    isSidebarOpen: types.optional(types.boolean, true),
    activeModal: types.maybeNull(types.string),

    // Application state for persistence
    currentRoute: types.optional(types.string, "/deck"), // Default route
    selectedDirectoryPath: types.maybeNull(types.string),
    selectedFileId: types.maybeNull(types.string),
  })
  .actions((self) => ({
    // Basic UI actions
    toggleSidebar() {
      self.isSidebarOpen = !self.isSidebarOpen;
    },
    openModal(modalName: string) {
      self.activeModal = modalName;
    },
    closeModal() {
      self.activeModal = null;
    },

    // Application state actions
    setCurrentRoute(route: string) {
      // Only update and save if the route is actually different
      if (route && route !== self.currentRoute) {
        self.currentRoute = route;
        // Save the state when route changes
        this.saveAppState();
        console.log(`Route updated and saved: ${route}`);
      }
    },

    setSelectedDirectoryPath(path: string | null) {
      self.selectedDirectoryPath = path;
      // Save the state when directory selection changes
      this.saveAppState();
    },

    setSelectedFileId(id: string | null) {
      self.selectedFileId = id;
      // Save the state when file selection changes
      this.saveAppState();
    },

    // Save application state to database
    saveAppState: flow(function* () {
      try {
        const appState = {
          currentRoute: self.currentRoute,
          selectedDirectoryPath: self.selectedDirectoryPath,
          selectedFileId: self.selectedFileId,
        };

        yield db.saveSetting({
          key: 'appState',
          value: appState
        });

        console.log("Application state saved successfully");
      } catch (error) {
        console.error("Failed to save application state:", error);
      }
    }),

    // Hydrate from database (called by RootStore)
    hydrateFromDb: flow(function* () {
      try {
        const appStateSetting = yield db.getSetting('appState');

        if (appStateSetting && appStateSetting.value) {
          const appState = appStateSetting.value;

          if (appState.currentRoute) {
            self.currentRoute = appState.currentRoute;
          }

          if (appState.selectedDirectoryPath !== undefined) {
            self.selectedDirectoryPath = appState.selectedDirectoryPath;
          }

          if (appState.selectedFileId !== undefined) {
            self.selectedFileId = appState.selectedFileId;
          }

          console.log("Application state loaded successfully");
        }
      } catch (error) {
        console.error("Failed to load application state:", error);
      }
    }),
  }));

// Use Instance for proper typing
export type UiStoreType = Instance<typeof UiStoreModel>;
export type UiStoreSnapshotType = any; // Simplified to avoid deprecated types
