# MobX-State-Tree (MST) TypeScript Editing Guidelines

This document outlines best practices and common pitfalls to avoid when editing MobX-State-Tree (MST) files within this project, particularly focusing on TypeScript integration. Following these guidelines should help reduce type errors and ensure smoother development.

## 1. Strict Type Checking

TypeScript enforces strict type checking. Ensure data passed between components, stores, and services (especially database interactions) precisely matches the defined interfaces (e.g., `StoredDirectoryHandle`, `TrackInfo`).

-   **Match Expected Types:** When calling functions (e.g., `db.saveTrackedDirectory`), the arguments MUST conform to the function's expected parameter types.
-   **Avoid `Omit` for Function Arguments:** If a function expects a complete interface, provide all properties, even if some are placeholders (like using `name` for `path` temporarily). Do not use `Omit` if the function signature requires the full type.
-   **Use `cast` Appropriately:** `cast` is primarily for informing TypeScript about types during *internal MST assignments* where you are certain of compatibility, but TypeScript cannot infer it (e.g., assigning plain objects to `self.scanProgress` or `self.lastScanCompletion`). Do NOT use `cast` to bypass type checks for function arguments expecting a different structure.

## 2. Flows (`flow`) for Async Actions

MST uses generators (`flow`) to manage asynchronous actions cleanly.

-   **Mandatory for Async:** Use `flow` for ALL asynchronous operations within MST actions (database calls, worker communication, API fetches).
-   **`yield` Promises:** Always `yield` the Promises returned by asynchronous functions called within a `flow` (e.g., `yield db.someAsyncFunction()`).
-   **Return Type:** Flows typically return `Promise<void>` or `Promise<Type>` based on the final yielded value. Explicit type annotations on the flow function itself are usually unnecessary.

## 3. Defining and Calling Actions

Structure actions correctly for MST's tracking and middleware capabilities.

-   **Structure:** Define helper generator functions (e.g., `_internalLoadData`) first. Then, create the main `actions` object and assign these helpers to the publicly exposed action names (e.g., `loadData: _internalLoadData`).
-   **Internal Calls:** Inside an action or flow, always call other actions via the `actions` object (e.g., `yield actions.loadData()`). Do *not* call the internal helper function (e.g., `_internalLoadData()`) directly from another action, as this bypasses MST mechanisms.
-   **Accessing `self`:** `self` refers to the store instance and is available within the `actions` function scope. Use it directly to modify store properties (e.g., `self.isLoading = true`).

## 4. Updating MST State

Use the appropriate methods for modifying store state.

-   **Bulk Updates (`applySnapshot`):** For replacing the entire contents of a map or array efficiently, use `applySnapshot(self.propertyName, newSnapshot)`. Ensure the `newSnapshot` object has the correct structure (e.g., for maps: keys are IDs, values are model-compatible snapshots).
-   **Single Item Modification:** To change properties of an *existing* model instance within the store, get the instance (e.g., `const user = self.users.get(id);`) and modify its properties directly within an action (e.g., `if (user) { user.isActive = false; }`).
-   **Adding/Removing Single Items:** Use the standard map/array methods provided by MST models within actions (e.g., `self.items.put(newItemModel)`, `self.items.delete(itemId)`).

## 5. Worker Interaction (If Applicable)

If using Web Workers with MST:

-   **Volatile State:** Store the worker instance in `volatile` state so it's not part of snapshots.
-   **Lifecycle Management:** Initialize (`afterCreate`) and terminate (`beforeDestroy`) the worker within MST lifecycle actions.
-   **Message Handling:** Handle worker messages (`onmessage`) and errors (`onerror`) by dispatching other defined MST actions (e.g., `worker.onmessage = (event) => actions.handleWorkerMessage(event.data);`).

## 6. Debugging

-   **`console.log`:** Use liberally within flows and actions to inspect data and trace execution flow.
-   **TypeScript Errors:** Read TypeScript error messages carefully. They often provide precise information about type mismatches or incorrect function usage.
-   **MST DevTools:** Consider using MobX-State-Tree DevTools for browser-based inspection of your store's state and actions.

By adhering to these guidelines, we aim to maintain type safety and leverage MST's features effectively.
