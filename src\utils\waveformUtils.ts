// Utility functions for waveform processing and rendering

export interface WaveformData {
  peaks: Float32Array;
  frequencyData?: {
    low: Float32Array;
    midLow: Float32Array;
    midHigh: Float32Array;
    high: Float32Array;
  };
  duration: number;
  sampleRate: number;
}

export interface WaveformRenderOptions {
  width: number;
  height: number;
  style: 'amplitude' | 'frequency';
  colors?: {
    amplitude?: string;
    frequency?: {
      low: string;
      midLow: string;
      midHigh: string;
      high: string;
    };
  };
  playheadColor?: string;
  backgroundColor?: string;
}

// Default color schemes
export const DEFAULT_COLORS = {
  amplitude: 'rgba(96, 165, 250, 0.7)',  // Lighter blue for unplayed portion
  amplitudePlayed: 'rgba(30, 64, 175, 0.9)', // Darker blue for played portion
  frequency: {
    midLow: 'rgba(30, 64, 175, 0.8)',     // Dark blue for low frequencies
    midHigh: 'rgba(59, 130, 246, 0.7)',  // Medium blue for mid-low
    high: 'rgba(96, 165, 250, 0.6)', // Light blue for mid-high
    // high: 'rgba(147, 197, 253, 0.5)',   // Very light blue for high frequencies
  },
  playhead: '#ffffff',
  background: 'transparent',
};

// Optimized waveform rendering function
export function renderWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  options: WaveformRenderOptions,
  currentTime: number = 0,
  deckNo: string = '0',
  trackId: string = '0'
): void {
  const { width, height, style } = options;
  const colors = { ...DEFAULT_COLORS, ...options.colors };

  // Clear canvas
  ctx.clearRect(0, 0, width, height);

  // Set background if specified
  if (options.backgroundColor && options.backgroundColor !== 'transparent') {
    ctx.fillStyle = options.backgroundColor;
    ctx.fillRect(0, 0, width, height);
  }

  const centerY = height / 2;

  if (style === 'amplitude') {
    renderAmplitudeWaveform(ctx, waveformData, width, height, centerY, colors.amplitude, currentTime);
  } else {
    renderFrequencyWaveform(ctx, waveformData, width, height, centerY, colors.frequency, currentTime, deckNo, trackId);
  }

  // Draw playhead
  if (waveformData.duration > 0) {
    const progress = currentTime / waveformData.duration;
    const playheadX = progress * width;

    ctx.strokeStyle = options.playheadColor || colors.playhead;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(playheadX, 0);
    ctx.lineTo(playheadX, height);
    ctx.stroke();
  }
}

function renderAmplitudeWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  color: string,
  currentTime: number = 0
): void {
  // Calculate progress position
  const progress = waveformData.duration > 0 ? currentTime / waveformData.duration : 0;
  const progressX = progress * width;

  // Calculate rendering parameters to ensure waveform fills entire width
  const peaksLength = waveformData.peaks.length;

  // Always render bars across the full width, scaling the data appropriately
  const barsToRender = Math.min(width, peaksLength * 2); // Allow some upsampling for clarity
  const barWidth = width / barsToRender;
  const effectiveBarWidth = Math.max(0.5, barWidth - 0.5); // Minimum bar width with spacing

  // Calculate how to map from canvas position to peaks data
  const peaksPerPixel = peaksLength / width;

  for (let i = 0; i < barsToRender; i++) {
    const x = i * barWidth;
    if (x >= width) break;

    // Map canvas position to peaks data index
    const peakIndex = (x / width) * peaksLength;
    const startSample = Math.floor(peakIndex);
    const endSample = Math.min(Math.ceil(peakIndex + peaksPerPixel), peaksLength - 1);

    let max = 0;
    let rms = 0;
    let sampleCount = 0;

    // Calculate both peak and RMS for better visual representation
    if (startSample === endSample) {
      // Single sample
      if (startSample < waveformData.peaks.length) {
        max = Math.abs(waveformData.peaks[startSample]);
        sampleCount = 1;
      }
    } else {
      // Multiple samples
      for (let j = startSample; j <= endSample && j < waveformData.peaks.length; j++) {
        const sample = Math.abs(waveformData.peaks[j]);
        max = Math.max(max, sample);
        rms += sample * sample;
        sampleCount++;
      }
    }

    if (sampleCount > 0) {
      rms = Math.sqrt(rms / sampleCount);
    }

    // Use a combination of peak and RMS for more natural-looking waveform
    const displayValue = sampleCount > 0 ? Math.max(max * 0.8, rms * 1.2) : 0;
    const barHeight = Math.max(1, displayValue * (height - 8));
    const y = centerY - barHeight / 2;

    // Determine if this bar is in the played or unplayed section
    const isPlayed = x < progressX;

    // Set color based on playback progress
    if (isPlayed) {
      // Played portion - darker blue
      ctx.fillStyle = DEFAULT_COLORS.amplitudePlayed;
    } else {
      // Unplayed portion - original color
      ctx.fillStyle = color;
    }

    // Draw the waveform bar with proper spacing
    ctx.fillRect(x, y, effectiveBarWidth, barHeight);

    // The spacing is automatically handled by using effectiveBarWidth
  }
}

function smoothBand(band: Float32Array, windowSize: number = 3): Float32Array {
  const smoothed = new Float32Array(band.length);
  const half = Math.floor(windowSize / 2);
  for (let i = 0; i < band.length; i++) {
    let sum = 0;
    let count = 0;
    for (let j = -half; j <= half; j++) {
      const idx = i + j;
      if (idx >= 0 && idx < band.length) {
        sum += band[idx];
        count++;
      }
    }
    smoothed[i] = count > 0 ? sum / count : band[i];
  }
  return smoothed;
}

// --- Cached geometry for frequency waveform rendering ---
interface CachedFrequencyGeometry {
  barXs: number[];
  barHeights: number[][]; // [bandIdx][barIdx]
  upperYs: number[][];
  lowerYs: number[][];
  effectiveBarWidth: number;
  barsToRender: number;
}

function computeFrequencyGeometry(
  frequencyData: { midLow: Float32Array; midHigh: Float32Array; high: Float32Array },
  width: number,
  height: number,
  centerY: number,
  smoothWindow: number = 5
): CachedFrequencyGeometry {
  const bands = [
    smoothBand(frequencyData.midLow, smoothWindow),
    smoothBand(frequencyData.midHigh, smoothWindow),
    smoothBand(frequencyData.high, smoothWindow)
  ];
  const bandsLength = bands[0].length;
  const barsToRender = Math.min(width, bandsLength * 2);
  const barWidth = width / barsToRender;
  const effectiveBarWidth = Math.max(0.5, barWidth - 0.5);
  const bandsPerPixel = bandsLength / width;
  const halfHeight = height / 2;

  // Precompute bar Xs
  const barXs: number[] = [];
  for (let i = 0; i < barsToRender; i++) {
    barXs.push(i * barWidth);
  }

  // Precompute bar heights and Y positions for each band and bar
  const barHeights: number[][] = [[], [], []];
  const upperYs: number[][] = [[], [], []];
  const lowerYs: number[][] = [[], [], []];

  for (let i = 0; i < barsToRender; i++) {
    const x = barXs[i];
    const bandIndex = (x / width) * bandsLength;
    const startSample = Math.floor(bandIndex);
    const endSample = Math.min(Math.ceil(bandIndex + bandsPerPixel), bandsLength - 1);
    let totalHeight = 0;
    for (let bandIdx = 0; bandIdx < bands.length; bandIdx++) {
      const band = bands[bandIdx];
      let max = 0;
      let rms = 0;
      let sampleCount = 0;
      if (startSample === endSample) {
        if (startSample < band.length) {
          max = Math.abs(band[startSample]);
          sampleCount = 1;
        }
      } else {
        for (let j = startSample; j <= endSample && j < band.length; j++) {
          const sample = Math.abs(band[j]);
          max = Math.max(max, sample);
          rms += sample * sample;
          sampleCount++;
        }
      }
      if (sampleCount > 0) {
        rms = Math.sqrt(rms / sampleCount);
      }
      const displayValue = sampleCount > 0 ? Math.max(max * 1.2, rms * 1.8) : 0;
      const bandHeight = Math.max(1, displayValue * halfHeight * 0.8);
      barHeights[bandIdx][i] = bandHeight;
      upperYs[bandIdx][i] = centerY - totalHeight - bandHeight;
      lowerYs[bandIdx][i] = centerY + totalHeight;
      totalHeight += bandHeight;
    }
  }
  return { barXs, barHeights, upperYs, lowerYs, effectiveBarWidth, barsToRender };
}

// --- Per-deck single-entry geometry cache for frequency waveform rendering ---
const freqGeomCache: Record<string, { key: string, cacheData: CachedFrequencyGeometry }> = {};

function detectBeatsInLowBand(
  lowBand: Float32Array,
  threshold: number = 0.6,
  minGap: number = 10
): number[] {
  // Detect local maxima above threshold, separated by at least minGap samples
  const beats: number[] = [];
  let lastBeatIdx = -minGap;
  for (let i = 1; i < lowBand.length - 1; i++) {
    if (
      lowBand[i] > threshold &&
      lowBand[i] > lowBand[i - 1] &&
      lowBand[i] > lowBand[i + 1] &&
      i - lastBeatIdx >= minGap
    ) {
      beats.push(i);
      lastBeatIdx = i;
    }
  }
  return beats;
}

function renderFrequencyWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  colors: { low?: string; midLow: string; midHigh: string, high: string },
  currentTime: number = 0,
  deckNo: string = '0',
  trackId: string = '0'
): void {
  const { frequencyData } = waveformData;
  if (!frequencyData) return;

  // Compose a key for the current track/geometry
  const freqKey = `${trackId}_${width}_${height}`;
  let cacheEntry = freqGeomCache[deckNo];
  let cachedFreqGeom: CachedFrequencyGeometry;
  if (cacheEntry && cacheEntry.key === freqKey) {
    cachedFreqGeom = cacheEntry.cacheData;
  } else {
    cachedFreqGeom = computeFrequencyGeometry(frequencyData, width, height, centerY, 5);
    freqGeomCache[deckNo] = { key: freqKey, cacheData: cachedFreqGeom };
  }
  const { barXs, barHeights, upperYs, lowerYs, effectiveBarWidth, barsToRender } = cachedFreqGeom;

  // Calculate progress position for frequency mode
  const progress = waveformData.duration > 0 ? currentTime / waveformData.duration : 0;
  const progressX = progress * width;
  const colorArray = [colors.midLow, colors.midHigh, colors.high];
  const playedColors = [
    'rgba(15, 32, 87, 0.8)',   // Darker version of low
    'rgba(30, 65, 123, 0.7)',  // Darker version of mid-low
    'rgba(48, 82, 125, 0.6)',  // Darker version of mid-high
  ];

  for (let i = 0; i < barsToRender; i++) {
    const x = barXs[i];
    if (x >= width) break;
    const isPlayed = x < progressX;
    for (let bandIdx = 0; bandIdx < 3; bandIdx++) {
      const bandHeight = barHeights[bandIdx][i];
      const upperY = upperYs[bandIdx][i];
      const lowerY = lowerYs[bandIdx][i];
      ctx.fillStyle = isPlayed ? playedColors[bandIdx] : colorArray[bandIdx];
      ctx.fillRect(x, upperY, effectiveBarWidth, bandHeight);
      ctx.fillRect(x, lowerY, effectiveBarWidth, bandHeight);
    }
  }

  // --- Draw beat markers using frequencyData.low ---
  if (frequencyData.low) {
      const beats = detectBeatsInLowBand(frequencyData.low, 0.5, frequencyData.low.length/width*3); // threshold and minGap can be tuned
    const markerHeight = height * 0.05; // 5% of total height
    ctx.save();
    ctx.lineWidth = 2;
    for (const beatIdx of beats) {
      // Map beatIdx to canvas X position
      const x = (beatIdx / frequencyData.low.length) * width;
      const isPlayed = x < progressX;
      // Use the same colors as midLow frequency band
      ctx.strokeStyle = isPlayed ? 'rgba(15, 32, 87, 0.8)' : colors.midLow;
      ctx.beginPath();
      ctx.moveTo(x, height - markerHeight);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    ctx.restore();
  }
}

// Calculate seek time from canvas click position
export function calculateSeekTime(
  event: MouseEvent,
  canvas: HTMLCanvasElement,
  duration: number
): number {
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const progress = x / rect.width;
  return Math.max(0, Math.min(duration, progress * duration));
}

// Optimize canvas for high DPI displays
export function setupHighDPICanvas(
  canvas: HTMLCanvasElement,
  width: number,
  height: number
): CanvasRenderingContext2D | null {
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  const devicePixelRatio = window.devicePixelRatio || 1;

  canvas.width = width * devicePixelRatio;
  canvas.height = height * devicePixelRatio;
  canvas.style.width = `${width}px`;
  canvas.style.height = `${height}px`;

  ctx.scale(devicePixelRatio, devicePixelRatio);

  return ctx;
}

// Debounce function for resize events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Performance monitoring utilities
export class WaveformPerformanceMonitor {
  private renderTimes: number[] = [];
  private maxSamples = 100;

  recordRenderTime(startTime: number): void {
    const renderTime = performance.now() - startTime;
    this.renderTimes.push(renderTime);

    if (this.renderTimes.length > this.maxSamples) {
      this.renderTimes.shift();
    }
  }

  getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0;
    return this.renderTimes.reduce((sum, time) => sum + time, 0) / this.renderTimes.length;
  }

  getMaxRenderTime(): number {
    return this.renderTimes.length > 0 ? Math.max(...this.renderTimes) : 0;
  }

  reset(): void {
    this.renderTimes = [];
  }
}

// Validate waveform data
export function validateWaveformData(data: any): data is WaveformData {
  return (
    data &&
    typeof data === 'object' &&
    data.peaks instanceof Float32Array &&
    typeof data.duration === 'number' &&
    typeof data.sampleRate === 'number' &&
    data.duration > 0 &&
    data.sampleRate > 0
  );
}
