// Define the interface for a crossfader curve function
export interface CrossfaderCurve {
  (x: number): [number, number];
}

// Define the available curve objects
export interface CurveObject {
  name: string;
  curve: CrossfaderCurve;
}

// Linear Crossfader Curve
export const linearCurve: CrossfaderCurve = (x) => {
  // Ensure x is within the 0-1 range
  const clampedX = Math.max(0, Math.min(1, x));
  const gainLeft = 1 - clampedX;
  const gainRight = clampedX;
  return [gainLeft, gainRight];
};

// Constant Power Crossfader Curve
export const constantPowerCurve: CrossfaderCurve = (x) => {
  // Ensure x is within the 0-1 range
  const clampedX = Math.max(0, Math.min(1, x));
  // Using the trigonometric approach for smoother transition
  const gainLeft = Math.cos(clampedX * Math.PI / 2);
  const gainRight = Math.sin(clampedX * Math.PI / 2);
  return [gainLeft, gainRight];
};

// Cut/Fast Cut Crossfader Curve
export const fastCutCurve: CrossfaderCurve = (x) => {
  // Ensure x is within the 0-1 range
  const clampedX = Math.max(0, Math.min(1, x));
  const gainLeft = Math.pow(1 - clampedX, 2);
  const gainRight = Math.pow(clampedX, 2);
  return [gainLeft, gainRight];
};

// Array of available curves
export const availableCurves: CurveObject[] = [
  { name: "Linear", curve: linearCurve },
  { name: "Constant Power", curve: constantPowerCurve },
  { name: "Fast Cut", curve: fastCutCurve },
];
