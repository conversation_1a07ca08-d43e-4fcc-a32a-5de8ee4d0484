// src/components/WelcomeScreen.tsx

import React, { useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { audioPermissionManager } from '../services/AudioPermissionManager';

interface WelcomeScreenProps {
  onPermissionGranted: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onPermissionGranted }) => {
  const [isRequesting, setIsRequesting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleStartApplication = async () => {
    setIsRequesting(true);
    setError(null);

    try {
      const granted = await audioPermissionManager.requestPermission();
      
      if (granted) {
        onPermissionGranted();
      } else {
        setError('Audio permission was denied. Please refresh the page and try again.');
      }
    } catch (err) {
      console.error('Error requesting audio permission:', err);
      setError('Failed to initialize audio. Please refresh the page and try again.');
    } finally {
      setIsRequesting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Welcome to Mismo.DJ</CardTitle>
          <CardDescription className="text-lg">
            Professional DJ Software
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <div className="text-sm text-muted-foreground">
              <p>
                This application requires audio access to function properly.
              </p>
              <p className="mt-2">
                Click the button below to enable audio and start the DJ application.
              </p>
            </div>
            
            {error && (
              <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}
          </div>

          <div className="flex flex-col space-y-3">
            <Button
              onClick={handleStartApplication}
              disabled={isRequesting}
              size="lg"
              className="w-full"
            >
              {isRequesting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Initializing Audio...
                </>
              ) : (
                'Start DJ Application'
              )}
            </Button>
            
            <div className="text-xs text-muted-foreground text-center">
              <p>
                By clicking "Start DJ Application", you grant permission for this
                application to use your device's audio capabilities.
              </p>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="text-xs text-muted-foreground space-y-1">
              <p><strong>Features:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Professional 4-deck mixing</li>
                <li>Advanced waveform visualization</li>
                <li>Beat synchronization and tempo control</li>
                <li>4-band equalizer with customizable frequencies</li>
                <li>Audio analysis and beat detection</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
