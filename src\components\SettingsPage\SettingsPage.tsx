import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label.tsx';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '../ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Slider } from '../ui/slider';
import EQVisualization from './EQVisualization';
import {
  lowEQFrequencies,
  midLoEQFrequencies,
  midHiEQFrequencies,
  highEQFrequencies,
  eqPresets,
  eqHardwarePresets,
  eqEffectPresets,
  EQEffectPreset
} from '../../stores/SettingsStore';

const SettingsPage: React.FC = observer(() => {
  const { settingsStore } = useStore();

  // State for custom frequency inputs
  const [customLowFreq, setCustomLowFreq] = useState(settingsStore.lowEQFrequency.toString());
  const [customMidFreq, setCustomMidFreq] = useState(settingsStore.midEQFrequency.toString());
  const [customMidLoFreq, setCustomMidLoFreq] = useState(settingsStore.midLoEQFrequency.toString());
  const [customMidHiFreq, setCustomMidHiFreq] = useState(settingsStore.midHiEQFrequency.toString());
  const [customHighFreq, setCustomHighFreq] = useState(settingsStore.highEQFrequency.toString());

  // State for EQ gain values in the visualization
  const [lowGain, setLowGain] = useState(0);
  const [midGain, setMidGain] = useState(0);
  const [midLoGain, setMidLoGain] = useState(0);
  const [midHiGain, setMidHiGain] = useState(0);
  const [highGain, setHighGain] = useState(0);

  // State for custom preset dialog
  const [customPresetName, setCustomPresetName] = useState('');
  const [showSavePresetDialog, setShowSavePresetDialog] = useState(false);

  const handleKeyNotationChange = (value: "standard" | "camelot") => {
    settingsStore.setKeyNotation(value);
  };

  const handleEqualizerFullKillChange = (checked: boolean) => {
    settingsStore.setEqualizerFullKill(checked);
  };

  const handleEQBandsChange = (value: "3-band" | "4-band") => {
    settingsStore.setEQBands(value);
  };

  const handleEQPresetChange = (value: string) => {
    settingsStore.setEQPreset(value);

    // Update custom frequency inputs to match the new preset
    const preset = eqPresets.find(p => p.name === value);
    if (preset) {
      setCustomLowFreq(preset.low.toString());

      if (preset.bands === 3 && preset.mid) {
        setCustomMidFreq(preset.mid.toString());
      } else if (preset.bands === 4) {
        if (preset.midLo) setCustomMidLoFreq(preset.midLo.toString());
        if (preset.midHi) setCustomMidHiFreq(preset.midHi.toString());
      }

      setCustomHighFreq(preset.high.toString());

      // Check if this is an effect preset with gain values
      const effectPreset = preset as EQEffectPreset;
      if (effectPreset.type === 'effect') {
        // Set gain values if they exist
        if (effectPreset.lowGain !== undefined) setLowGain(effectPreset.lowGain);

        if (preset.bands === 3) {
          if (effectPreset.midGain !== undefined) setMidGain(effectPreset.midGain);
        } else {
          if (effectPreset.midLoGain !== undefined) setMidLoGain(effectPreset.midLoGain);
          if (effectPreset.midHiGain !== undefined) setMidHiGain(effectPreset.midHiGain);
        }

        if (effectPreset.highGain !== undefined) setHighGain(effectPreset.highGain);
      } else {
        // Reset gain values for hardware presets
        setLowGain(0);
        setMidGain(0);
        setMidLoGain(0);
        setMidHiGain(0);
        setHighGain(0);
      }
    }
  };

  // Handlers for EQ frequency changes from dropdown
  const handleLowEQFrequencyChange = (value: string) => {
    settingsStore.setLowEQFrequency(parseInt(value));
    setCustomLowFreq(value);
  };

  const handleMidEQFrequencyChange = (value: string) => {
    settingsStore.setMidEQFrequency(parseInt(value));
    setCustomMidFreq(value);
  };

  const handleMidLoEQFrequencyChange = (value: string) => {
    settingsStore.setMidLoEQFrequency(parseInt(value));
    setCustomMidLoFreq(value);
  };

  const handleMidHiEQFrequencyChange = (value: string) => {
    settingsStore.setMidHiEQFrequency(parseInt(value));
    setCustomMidHiFreq(value);
  };

  const handleHighEQFrequencyChange = (value: string) => {
    settingsStore.setHighEQFrequency(parseInt(value));
    setCustomHighFreq(value);
  };

  // Handlers for custom frequency inputs
  const handleCustomLowFreqChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomLowFreq(e.target.value);
  };

  const handleCustomMidFreqChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomMidFreq(e.target.value);
  };

  const handleCustomMidLoFreqChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomMidLoFreq(e.target.value);
  };

  const handleCustomMidHiFreqChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomMidHiFreq(e.target.value);
  };

  const handleCustomHighFreqChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomHighFreq(e.target.value);
  };

  // Apply custom frequency values
  const applyCustomLowFreq = () => {
    const freq = parseInt(customLowFreq);
    if (!isNaN(freq) && freq > 0) {
      settingsStore.setLowEQFrequency(freq);
    }
  };

  const applyCustomMidFreq = () => {
    const freq = parseInt(customMidFreq);
    if (!isNaN(freq) && freq > 0) {
      settingsStore.setMidEQFrequency(freq);
    }
  };

  const applyCustomMidLoFreq = () => {
    const freq = parseInt(customMidLoFreq);
    if (!isNaN(freq) && freq > 0) {
      settingsStore.setMidLoEQFrequency(freq);
    }
  };

  const applyCustomMidHiFreq = () => {
    const freq = parseInt(customMidHiFreq);
    if (!isNaN(freq) && freq > 0) {
      settingsStore.setMidHiEQFrequency(freq);
    }
  };

  const applyCustomHighFreq = () => {
    const freq = parseInt(customHighFreq);
    if (!isNaN(freq) && freq > 0) {
      settingsStore.setHighEQFrequency(freq);
    }
  };

  // EQ gain values are handled directly by the sliders

  // Handler for frequency change in the visualization
  const handleVisualizationFrequencyChange = (type: 'low' | 'mid' | 'midLo' | 'midHi' | 'high', value: number) => {
    switch (type) {
      case 'low':
        setCustomLowFreq(value.toString());
        settingsStore.setLowEQFrequency(value);
        break;
      case 'mid':
        setCustomMidFreq(value.toString());
        settingsStore.setMidEQFrequency(value);
        break;
      case 'midLo':
        setCustomMidLoFreq(value.toString());
        settingsStore.setMidLoEQFrequency(value);
        break;
      case 'midHi':
        setCustomMidHiFreq(value.toString());
        settingsStore.setMidHiEQFrequency(value);
        break;
      case 'high':
        setCustomHighFreq(value.toString());
        settingsStore.setHighEQFrequency(value);
        break;
    }
  };

  // Handler for saving a custom preset
  const handleSaveCustomPreset = () => {
    if (!customPresetName.trim()) return;

    const newPreset: EQEffectPreset = {
      name: customPresetName,
      type: 'effect',
      low: settingsStore.lowEQFrequency,
      mid: settingsStore.eqBands === "3-band" ? settingsStore.midEQFrequency : undefined,
      midLo: settingsStore.eqBands === "4-band" ? settingsStore.midLoEQFrequency : undefined,
      midHi: settingsStore.eqBands === "4-band" ? settingsStore.midHiEQFrequency : undefined,
      high: settingsStore.highEQFrequency,
      bands: settingsStore.eqBands === "3-band" ? 3 : 4,
      lowGain: lowGain,
      midGain: settingsStore.eqBands === "3-band" ? midGain : undefined,
      midLoGain: settingsStore.eqBands === "4-band" ? midLoGain : undefined,
      midHiGain: settingsStore.eqBands === "4-band" ? midHiGain : undefined,
      highGain: highGain
    };

    settingsStore.saveCustomEQPreset(newPreset);
    setCustomPresetName('');
    setShowSavePresetDialog(false);
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Settings</h2>

      <div className="mb-4">
        <Label htmlFor="deck-count-select" className="block text-sm font-medium text-gray-700 mb-1">Number of Decks</Label>
        <Select
          value={settingsStore.numberOfDecks.toString()}
          onValueChange={(value) => {
            const numDecks = parseInt(value, 10) as 1 | 2 | 4;
            // Ensure numDecks is one of the allowed values before calling the store action
            if (numDecks === 1 || numDecks === 2 || numDecks === 4) {
              settingsStore.setNumberOfDecks(numDecks);
            }
          }}
        >
          <SelectTrigger id="deck-count-select" className="w-[180px]">
            <SelectValue placeholder="Select number of decks" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 Deck</SelectItem>
            <SelectItem value="2">2 Decks</SelectItem>
            <SelectItem value="4">4 Decks</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Key Notation</label>
        <Select onValueChange={handleKeyNotationChange} value={settingsStore.useKeyNotation}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select notation" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="standard">Standard</SelectItem>
            <SelectItem value="camelot">Camelot</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="mb-4">
        <div className="flex items-center space-x-2 mb-1">
          <Switch
            id="equalizer-full-kill"
            checked={settingsStore.equalizerFullKill}
            onCheckedChange={handleEqualizerFullKillChange}
          />
          <Label htmlFor="equalizer-full-kill" className="text-sm font-medium text-gray-700">
            Equalizer Full Kill
          </Label>
        </div>
        <p className="text-sm text-gray-500 mt-1">
          When enabled, equalizer bands can completely silence specific frequency ranges (-Infinity dB).
          When disabled, the minimum is limited to -12 dB.
        </p>
      </div>

      <h3 className="text-lg font-semibold mb-2">Equalizer Settings</h3>

      {/* EQ Visualization */}
      <div className="mb-6 border border-gray-200 rounded-lg p-4 bg-gray-50">
        <h4 className="text-md font-semibold mb-3">Equalizer Visualization</h4>

        <EQVisualization
          lowFreq={settingsStore.lowEQFrequency}
          midFreq={settingsStore.eqBands === "3-band" ? settingsStore.midEQFrequency : undefined}
          midLoFreq={settingsStore.eqBands === "4-band" ? settingsStore.midLoEQFrequency : undefined}
          midHiFreq={settingsStore.eqBands === "4-band" ? settingsStore.midHiEQFrequency : undefined}
          highFreq={settingsStore.highEQFrequency}
          lowGain={lowGain}
          midGain={midGain}
          midLoGain={midLoGain}
          midHiGain={midHiGain}
          highGain={highGain}
          is4BandMode={settingsStore.eqBands === "4-band"}
          isFullKill={settingsStore.equalizerFullKill}
          onFrequencyChange={handleVisualizationFrequencyChange}
        />

        <div className="mt-4 flex justify-between">
          <div className="flex space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Low Gain</label>
              <Slider
                min={-12}
                max={12}
                step={0.1}
                value={[lowGain]}
                onValueChange={(values: number[]) => setLowGain(values[0])}
                className="w-[100px]"
              />
              <div className="text-xs text-center mt-1">{lowGain.toFixed(1)} dB</div>
            </div>

            {settingsStore.eqBands === "3-band" ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mid Gain</label>
                <Slider
                  min={-12}
                  max={12}
                  step={0.1}
                  value={[midGain]}
                  onValueChange={(values: number[]) => setMidGain(values[0])}
                  className="w-[100px]"
                />
                <div className="text-xs text-center mt-1">{midGain.toFixed(1)} dB</div>
              </div>
            ) : (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Mid-Lo Gain</label>
                  <Slider
                    min={-12}
                    max={12}
                    step={0.1}
                    value={[midLoGain]}
                    onValueChange={(values: number[]) => setMidLoGain(values[0])}
                    className="w-[100px]"
                  />
                  <div className="text-xs text-center mt-1">{midLoGain.toFixed(1)} dB</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Mid-Hi Gain</label>
                  <Slider
                    min={-12}
                    max={12}
                    step={0.1}
                    value={[midHiGain]}
                    onValueChange={(values: number[]) => setMidHiGain(values[0])}
                    className="w-[100px]"
                  />
                  <div className="text-xs text-center mt-1">{midHiGain.toFixed(1)} dB</div>
                </div>
              </>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">High Gain</label>
              <Slider
                min={-12}
                max={12}
                step={0.1}
                value={[highGain]}
                onValueChange={(values: number[]) => setHighGain(values[0])}
                className="w-[100px]"
              />
              <div className="text-xs text-center mt-1">{highGain.toFixed(1)} dB</div>
            </div>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                // Reset all gain values to 0
                setLowGain(0);
                setMidGain(0);
                setMidLoGain(0);
                setMidHiGain(0);
                setHighGain(0);
              }}
            >
              Reset to Default
            </Button>

            <Dialog open={showSavePresetDialog} onOpenChange={setShowSavePresetDialog}>
              <DialogTrigger asChild>
                <Button variant="outline">Save as Preset</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Save Custom EQ Preset</DialogTitle>
                </DialogHeader>
                <div className="py-4">
                  <label className="block text-sm font-medium mb-1">Preset Name</label>
                  <Input
                    value={customPresetName}
                    onChange={(e) => setCustomPresetName(e.target.value)}
                    placeholder="Enter preset name"
                  />
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowSavePresetDialog(false)}>Cancel</Button>
                  <Button onClick={handleSaveCustomPreset}>Save Preset</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* EQ Bands Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">EQ Mode</label>
        <Select
          onValueChange={handleEQBandsChange}
          value={settingsStore.eqBands}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select EQ mode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="3-band">3-Band EQ</SelectItem>
            <SelectItem value="4-band">4-Band EQ</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-gray-500 mt-1">
          3-Band: Low, Mid, High<br />
          4-Band: Low, Mid-Lo, Mid-Hi, High
        </p>
      </div>

      {/* EQ Preset Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">EQ Preset</label>
        <Tabs defaultValue="hardware" className="w-full">
          <TabsList className="mb-2">
            <TabsTrigger value="hardware">Hardware Emulation</TabsTrigger>
            <TabsTrigger value="effects">Effect Presets</TabsTrigger>
            <TabsTrigger value="custom">Custom Presets</TabsTrigger>
          </TabsList>

          <TabsContent value="hardware">
            <Select
              onValueChange={handleEQPresetChange}
              value={settingsStore.eqPreset}
            >
              <SelectTrigger className="w-[280px]">
                <SelectValue placeholder="Select hardware preset" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Custom">Custom</SelectItem>
                {eqHardwarePresets.map((preset) => (
                  <SelectItem key={preset.name} value={preset.name}>
                    {preset.name} ({preset.bands}-band)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500 mt-1">
              Select a preset that emulates popular DJ equipment.
            </p>
          </TabsContent>

          <TabsContent value="effects">
            <Select
              onValueChange={handleEQPresetChange}
              value={settingsStore.eqPreset}
            >
              <SelectTrigger className="w-[280px]">
                <SelectValue placeholder="Select effect preset" />
              </SelectTrigger>
              <SelectContent>
                {eqEffectPresets.map((preset) => (
                  <SelectItem key={preset.name} value={preset.name}>
                    {preset.name} ({preset.bands}-band)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500 mt-1">
              Select a preset with predefined EQ curves for specific effects.
            </p>
          </TabsContent>

          <TabsContent value="custom">
            <Select
              onValueChange={handleEQPresetChange}
              value={settingsStore.eqPreset}
            >
              <SelectTrigger className="w-[280px]">
                <SelectValue placeholder="Select custom preset" />
              </SelectTrigger>
              <SelectContent>
                {settingsStore.getCustomEQPresets().map((preset) => (
                  <SelectItem key={preset.name} value={preset.name}>
                    {preset.name} ({preset.bands}-band)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500 mt-1">
              Select one of your saved custom presets.
            </p>
          </TabsContent>
        </Tabs>
      </div>

      <h3 className="text-lg font-semibold mb-2">Equalizer Frequencies</h3>

      {/* Low EQ Frequency */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Low EQ Frequency</label>
        <div className="flex space-x-2">
          <Select
            onValueChange={handleLowEQFrequencyChange}
            value={lowEQFrequencies.includes(settingsStore.lowEQFrequency) ? settingsStore.lowEQFrequency.toString() : "custom"}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              {lowEQFrequencies.map((freq: number) => (
                <SelectItem key={freq} value={freq.toString()}>{freq} Hz</SelectItem>
              ))}
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={customLowFreq}
              onChange={handleCustomLowFreqChange}
              className="w-[100px]"
              min="20"
              max="1000"
            />
            <Button onClick={applyCustomLowFreq}>Apply</Button>
          </div>
        </div>
        <p className="text-sm text-gray-500 mt-1">Current: {settingsStore.lowEQFrequency} Hz</p>
      </div>

      {settingsStore.eqBands === "3-band" ? (
        // Mid EQ Frequency (3-band mode)
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Mid EQ Frequency</label>
          <div className="flex space-x-2">
            <Select
              onValueChange={handleMidEQFrequencyChange}
              value={midLoEQFrequencies.includes(settingsStore.midEQFrequency) ? settingsStore.midEQFrequency.toString() : "custom"}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                {midLoEQFrequencies.map((freq: number) => (
                  <SelectItem key={freq} value={freq.toString()}>{freq} Hz</SelectItem>
                ))}
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex space-x-2">
              <Input
                type="number"
                value={customMidFreq}
                onChange={handleCustomMidFreqChange}
                className="w-[100px]"
                min="200"
                max="5000"
              />
              <Button onClick={applyCustomMidFreq}>Apply</Button>
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-1">Current: {settingsStore.midEQFrequency} Hz</p>
        </div>
      ) : (
        // Mid-Lo and Mid-Hi EQ Frequencies (4-band mode)
        <>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Mid-Lo EQ Frequency</label>
            <div className="flex space-x-2">
              <Select
                onValueChange={handleMidLoEQFrequencyChange}
                value={midLoEQFrequencies.includes(settingsStore.midLoEQFrequency) ? settingsStore.midLoEQFrequency.toString() : "custom"}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  {midLoEQFrequencies.map((freq: number) => (
                    <SelectItem key={freq} value={freq.toString()}>{freq} Hz</SelectItem>
                  ))}
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex space-x-2">
                <Input
                  type="number"
                  value={customMidLoFreq}
                  onChange={handleCustomMidLoFreqChange}
                  className="w-[100px]"
                  min="200"
                  max="2000"
                />
                <Button onClick={applyCustomMidLoFreq}>Apply</Button>
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-1">Current: {settingsStore.midLoEQFrequency} Hz</p>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Mid-Hi EQ Frequency</label>
            <div className="flex space-x-2">
              <Select
                onValueChange={handleMidHiEQFrequencyChange}
                value={midHiEQFrequencies.includes(settingsStore.midHiEQFrequency) ? settingsStore.midHiEQFrequency.toString() : "custom"}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  {midHiEQFrequencies.map((freq: number) => (
                    <SelectItem key={freq} value={freq.toString()}>{freq} Hz</SelectItem>
                  ))}
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex space-x-2">
                <Input
                  type="number"
                  value={customMidHiFreq}
                  onChange={handleCustomMidHiFreqChange}
                  className="w-[100px]"
                  min="1000"
                  max="8000"
                />
                <Button onClick={applyCustomMidHiFreq}>Apply</Button>
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-1">Current: {settingsStore.midHiEQFrequency} Hz</p>
          </div>
        </>
      )}

      {/* High EQ Frequency */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">High EQ Frequency</label>
        <div className="flex space-x-2">
          <Select
            onValueChange={handleHighEQFrequencyChange}
            value={highEQFrequencies.includes(settingsStore.highEQFrequency) ? settingsStore.highEQFrequency.toString() : "custom"}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              {highEQFrequencies.map((freq: number) => (
                <SelectItem key={freq} value={freq.toString()}>{freq} Hz</SelectItem>
              ))}
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={customHighFreq}
              onChange={handleCustomHighFreqChange}
              className="w-[100px]"
              min="2000"
              max="20000"
            />
            <Button onClick={applyCustomHighFreq}>Apply</Button>
          </div>
        </div>
        <p className="text-sm text-gray-500 mt-1">Current: {settingsStore.highEQFrequency} Hz</p>
      </div>

      {/* Master Deck and Sync Settings */}
      <h3 className="text-lg font-semibold mb-4 mt-8">Master Deck & Sync Settings</h3>

      {/* Auto Master Mode */}
      <div className="mb-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="auto-master-mode"
            checked={settingsStore.autoMasterMode}
            onCheckedChange={(checked) => settingsStore.setAutoMasterMode(checked)}
          />
          <Label htmlFor="auto-master-mode" className="text-sm font-medium">
            Auto Master Mode
          </Label>
        </div>
        <p className="text-sm text-gray-500 mt-1">
          Automatically select master deck based on playback activity and audio levels.
        </p>
      </div>

      {/* Sync Granularity */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Sync Granularity</label>
        <Select
          onValueChange={(value: "beat" | "half-beat" | "quarter-beat") => settingsStore.setSyncGranularity(value)}
          value={settingsStore.syncGranularity}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select sync granularity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="beat">Beat (1/1)</SelectItem>
            <SelectItem value="half-beat">Half Beat (1/2)</SelectItem>
            <SelectItem value="quarter-beat">Quarter Beat (1/4)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-gray-500 mt-1">
          Determines how precisely tracks are aligned when syncing.
        </p>
      </div>

      {/* Max Sync Pitch Range */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Max Sync Pitch Range</label>
        <Select
          onValueChange={(value: "10" | "20" | "unlimited") => settingsStore.setMaxSyncPitchRange(value)}
          value={settingsStore.maxSyncPitchRange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select pitch range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">±10%</SelectItem>
            <SelectItem value="20">±20%</SelectItem>
            <SelectItem value="unlimited">Unlimited</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-gray-500 mt-1">
          Maximum pitch adjustment allowed when syncing tracks. Prevents extreme tempo changes.
        </p>
      </div>

      {/* Add other settings here in the future */}
    </div>
  );
});

export default SettingsPage;
