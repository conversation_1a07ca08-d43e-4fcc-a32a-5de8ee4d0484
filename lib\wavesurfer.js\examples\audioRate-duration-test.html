<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>AudioRate Duration Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    #waveform {
      margin: 20px 0;
    }
    .controls {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }
    .control-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    button {
      padding: 8px 16px;
      cursor: pointer;
    }
    .time-display {
      margin-top: 20px;
      font-family: monospace;
      font-size: 16px;
    }
    .time-display div {
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <h1>AudioRate Duration Test</h1>
  <p>This example demonstrates how changing the audioRate affects the duration and current time.</p>

  <div class="controls">
    <div class="control-group">
      <label>Playback Rate: <span id="rateValue">1.00</span>x</label>
      <input type="range" id="rateSlider" min="0.25" max="4" step="0.25" value="1">
    </div>
    <div class="control-group">
      <button id="playPause">Play</button>
    </div>
  </div>

  <div id="waveform"></div>

  <div class="time-display">
    <div>Current Time: <span id="currentTime">0.00</span> seconds</div>
    <div>Duration: <span id="totalTime">0.00</span> seconds</div>
    <div>Progress: <span id="progress">0.00</span>%</div>
  </div>

  <script type="module">
    import WaveSurfer from '../dist/wavesurfer.esm.js'

    // Create an instance of WaveSurfer
    const wavesurfer = WaveSurfer.create({
      container: '#waveform',
      waveColor: 'rgb(0, 200, 200)',
      progressColor: 'rgb(0, 100, 100)',
      url: '/examples/audio/audio.wav',
      minPxPerSec: 100,
      height: 100,
      audioRate: 1.0, // Initial playback rate
    })

    // Display initial total duration
    wavesurfer.on('ready', () => {
      document.querySelector('#totalTime').textContent = wavesurfer.getDuration().toFixed(2)
    })

    // Play on click
    wavesurfer.on('interaction', () => {
      wavesurfer.play()
    })

    // Display current time and update progress
    wavesurfer.on('timeupdate', (currentTime) => {
      document.querySelector('#currentTime').textContent = currentTime.toFixed(2)
      document.querySelector('#totalTime').textContent = wavesurfer.getDuration().toFixed(2)
      
      // Calculate and display progress percentage
      const progress = (currentTime / wavesurfer.getDuration()) * 100
      document.querySelector('#progress').textContent = progress.toFixed(2)
    })

    // Set up UI controls
    document.addEventListener('DOMContentLoaded', () => {
      // Play/Pause button
      const playPauseBtn = document.querySelector('#playPause')
      playPauseBtn.addEventListener('click', () => {
        wavesurfer.playPause()
      })
      
      wavesurfer.on('play', () => {
        playPauseBtn.textContent = 'Pause'
      })
      
      wavesurfer.on('pause', () => {
        playPauseBtn.textContent = 'Play'
      })

      // Rate slider
      document.querySelector('#rateSlider').addEventListener('input', (e) => {
        const rate = parseFloat(e.target.value)
        document.querySelector('#rateValue').textContent = rate.toFixed(2)
        wavesurfer.setPlaybackRate(rate)
        
        // Update duration display immediately
        document.querySelector('#totalTime').textContent = wavesurfer.getDuration().toFixed(2)
        
        // Update progress percentage
        const currentTime = wavesurfer.getCurrentTime()
        const progress = (currentTime / wavesurfer.getDuration()) * 100
        document.querySelector('#progress').textContent = progress.toFixed(2)
      })
    })
  </script>
</body>
</html>
