import { RootStoreModel, RootStoreType } from "./RootStore";

// Create an initial snapshot if needed, otherwise use an empty object
const initialSnapshot = {};

// Create the root store instance
const rootStore: RootStoreType = RootStoreModel.create(initialSnapshot);

// You can add environment dependencies here if needed, e.g., API clients
// const env = { api: new ApiClient() };
// const rootStore = RootStoreModel.create(initialSnapshot, env);

// Export the instance
export default rootStore;

// Optional: Export the type for convenience
export type { RootStoreType };
