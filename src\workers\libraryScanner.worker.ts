/// <reference lib="webworker" />
import { db, StoredDirectoryHandle, TrackInfo } from '../services/DatabaseService';
// NOTE: For optimal performance, add a compound index for '[directoryId+fileHash]'
// to the 'tracks' table definition in src/services/DatabaseService.ts
// Example: db.version(X).stores({ tracks: '++id, directoryId, fileHash, [directoryId+fileHash], ...' });
// @ts-ignore
import jsmediatags from 'jsmediatags/dist/jsmediatags.min.js'; // Import jsmediatags dist need to be used - DO NOT CHANGE
import { TagType } from 'jsmediatags/types';
import { sha256 } from 'js-sha256';

// Define supported file types (adjust as needed)
const SUPPORTED_AUDIO_EXTENSIONS = ['.mp3', '.wav', '.flac', '.m4a', '.ogg', '.aac'];
const SUPPORTED_VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi', '.mkv']; // Add video if needed
const SUPPORTED_EXTENSIONS = [...SUPPORTED_AUDIO_EXTENSIONS, ...SUPPORTED_VIDEO_EXTENSIONS];

// --- Interfaces for Worker Communication ---
interface ScanRequestMessage {
  type: 'SCAN_FOLDERS';
  directoryIds?: string[]; // Optional: Scan specific directories, otherwise scan all
}

interface ScanProgressMessage {
  type: 'SCAN_PROGRESS';
  processed: number;
  total: number;
  currentFolder?: string;
  error?: string; // Report specific file errors
}

// @ts-ignore- for future use
interface ScanCompleteMessage {
  type: 'SCAN_COMPLETE';
  added: number;
  updated: number;
  errors: number;
  totalDuration: number; // In seconds
}

// --- Helper Functions ---

/**
 * Parses Artist/Title from filename (simple example)
 */
function parseFilename(filename: string): { artist?: string; title?: string } {
    const cleanedName = filename.substring(0, filename.lastIndexOf('.')) || filename; // Remove extension
    const parts = cleanedName.split(' - ');
    if (parts.length >= 2) {
        return { artist: parts[0].trim(), title: parts.slice(1).join(' - ').trim() };
    }
    // Fallback: Use the whole name as title if no separator found
    return { title: cleanedName.trim() };
}

/**
 * Calculates the SHA-256 hash of a file.
 */
async function calculateFileHash(file: File): Promise<string> {
    const buffer = await file.arrayBuffer();
    return sha256(buffer);
}

/**
 * Reads metadata using jsmediatags
 */
async function readMediaTags(file: File): Promise<TagType | {}> { // Return TagType or empty object
    return new Promise((resolve) => { // No reject needed based on logic
        new jsmediatags.Reader(file)
            .read({
                onSuccess: (tagResult: TagType) => resolve(tagResult), // onSuccess likely returns TagType directly
                onError: (error: any) => { // Use 'any' for error type if ErrorType is not exported
                    console.warn(`jsmediatags error for ${file.name}:`, error?.type, error?.info); // Use optional chaining
                    // Don't reject, just resolve with empty object if tags are unreadable
                    resolve({}); // Resolve with empty object on error
                }
            });
    });
}

/**
 * Recursively scans a directory handle
 */
async function scanDirectory(
    dirHandle: FileSystemDirectoryHandle,
    directoryId: string,
    basePath: string = '',
    progressCallback: (update: Partial<ScanProgressMessage>) => void
): Promise<{ added: number; updated: number; errors: number; filesProcessed: number }> {
    let count = { added: 0, updated: 0, errors: 0, filesProcessed: 0 };
    const entries = [];
    for await (const entry of dirHandle.values()) {
        entries.push(entry); // Collect entries first
    }

    progressCallback({ total: (progressCallback as any).total + entries.length }); // Update total count

    for await (const entry of entries) {
        const currentPath = basePath ? `${basePath}/${entry.name}` : entry.name;
        if (entry.kind === 'file') {
            const fileExtension = entry.name.substring(entry.name.lastIndexOf('.')).toLowerCase();
            if (SUPPORTED_EXTENSIONS.includes(fileExtension)) {
                try {
                    const file = await entry.getFile();
                    const trackId = `${directoryId}:${currentPath}`; // Unique ID for this specific path

                    // --- Calculate File Hash ---
                    let fileHash: string | undefined;
                    try {
                        fileHash = await calculateFileHash(file);
                    } catch (hashError) {
                        console.error(`Error calculating hash for ${currentPath}:`, hashError);
                        progressCallback({ error: `Hashing error for ${entry.name}` });
                        // Decide if we should continue without hash or count as error and skip
                        count.errors++;
                        continue; // Skip this file if hashing fails
                    }

                    // --- Primary Check: Existing track by Hash *within the same directory* ---
                    let existingTrackByHashInDir: TrackInfo | undefined;
                    if (fileHash) {
                        // Use filter for robustness if compound index '[directoryId+fileHash]' isn't available/reliable
                        const potentialMatches = await db.tracks.where('fileHash').equals(fileHash).toArray();
                        existingTrackByHashInDir = potentialMatches.find(track => track.id.startsWith(directoryId + ':'));

                        // // Ideal approach with compound index (uncomment if index is added):
                        // existingTrackByHashInDir = await db.tracks
                        //     .where('[directoryId+fileHash]')
                        //     .equals([directoryId, fileHash])
                        //     .first();
                    }

                    if (existingTrackByHashInDir) {
                        // Found existing track with same hash in the same directory. Update it.
                        console.log(`File ${currentPath} matches hash of existing track ${existingTrackByHashInDir.id} in same directory. Updating.`);

                        const tags = await readMediaTags(file);
                        const filenameParts = parseFilename(entry.name);
                        const getTagProp = createTagGetter(tags);

                        await db.tracks.update(existingTrackByHashInDir.id, {
                            filePath: currentPath, // Update path in case of rename
                            filename: entry.name, // Update filename in case of rename
                            artist: getTagProp('artist') || filenameParts.artist || existingTrackByHashInDir.artist,
                            title: getTagProp('title') || filenameParts.title || existingTrackByHashInDir.title,
                            album: getTagProp('album') || existingTrackByHashInDir.album,
                            genre: getTagProp('genre') || existingTrackByHashInDir.genre,
                            duration: getTagProp('duration') || existingTrackByHashInDir.duration,
                            isHidden: 0, // Ensure it's visible
                            analysisStatus: existingTrackByHashInDir.analysisStatus === "Error" ? "Pending" : existingTrackByHashInDir.analysisStatus, // Reset error status maybe?
                            // Keep existing addedDate, lastPlayedDate, rating, etc.
                        });
                        count.updated++;
                        count.filesProcessed++;
                        progressCallback({ processed: (progressCallback as any).processed + 1 });
                        continue; // Move to the next file
                    }

                    // --- No match by hash in the same directory. Proceed with path-based check (add new or update existing by path) ---
                    console.log(`Processing file ${currentPath} (Hash: ${fileHash?.substring(0, 8)}...). No hash match in same directory.`);
                    const tags = await readMediaTags(file);
                    const filenameParts = parseFilename(entry.name);
                    const getTagProp = createTagGetter(tags); // Use helper for tag access

                    // Helper function to get tag properties safely
                    function createTagGetter(tagsObj: TagType | {}) {
                        return (prop: keyof TagType['tags']) => {
                            if ('tags' in tagsObj && tagsObj.tags && tagsObj.tags[prop]) {
                                const tagValue = tagsObj.tags[prop];
                                if (typeof tagValue === 'object' && tagValue !== null && 'data' in tagValue) {
                                    // Handle specific cases like picture data if needed
                                    if (prop === 'picture' && typeof tagValue.data === 'object') return undefined; // Don't store raw picture data
                                    return tagValue.data;
                                }
                                return tagValue; // Primitive value
                            }
                            return undefined;
                        };
                    }


                    // Check if a track exists with this *exact path* (ID) already
                    const existingTrackById = await db.tracks.get(trackId);

                    const trackData: Partial<TrackInfo> = {
                        // id: trackId, // ID is the key, not needed in data for 'put' or 'update'
                        directoryId: directoryId, // Add the directory ID here
                        filePath: currentPath,
                        filename: entry.name,
                        duration: getTagProp('duration'),
                        artist: getTagProp('artist') || filenameParts.artist,
                        title: getTagProp('title') || filenameParts.title,
                        album: getTagProp('album'),
                        genre: getTagProp('genre'),
                        isHidden: 0, // Ensure not hidden
                        analysisStatus: "Pending", // Assume re-scan means re-analyze if needed
                        fileHash: fileHash, // Store the hash
                        duplicateOf: undefined, // Not a duplicate by hash
                        // Keep existing rating, addedDate, lastPlayedDate, analysisStatus if updating
                    };


                    if (existingTrackById) {
                        // Track with this path exists. Update it (could be metadata change or file overwrite).
                        console.log(`Updating existing track by ID/Path: ${trackId}`);
                        // Preserve some fields from the existing record
                        trackData.addedDate = existingTrackById.addedDate; // Keep original added date
                        trackData.lastPlayedDate = existingTrackById.lastPlayedDate;
                        trackData.rating = existingTrackById.rating;
                        // Merge existing analysis status unless it was Error, otherwise reset to Pending
                        trackData.analysisStatus = existingTrackById.analysisStatus === "Error" ? "Pending" : existingTrackById.analysisStatus;
                        trackData.isHidden = 0; // Ensure visible

                        await db.tracks.update(trackId, trackData);
                        count.updated++;
                    } else {
                        // Track with this path does not exist. Add it as a new track.
                        console.log(`Adding new track: ${trackId}`);
                        trackData.id = trackId; // Need ID for adding
                        trackData.addedDate = Date.now(); // Set added date for new track
                        trackData.isHidden = 0; // Ensure visible
                        trackData.analysisStatus = "Pending"; // New track needs analysis
                        await db.tracks.put(trackData as TrackInfo); // Cast needed as we built Partial<TrackInfo>
                        count.added++;
                    }

                } catch (error: any) {
                    console.error(`Error processing file ${currentPath} (after hash check):`, error);
                    progressCallback({ error: `Error processing ${entry.name}: ${error.message}` });
                    count.errors++;
                } finally {
                    count.filesProcessed++;
                    progressCallback({ processed: (progressCallback as any).processed + 1 });
                }
            }
        } else if (entry.kind === 'directory') {
            progressCallback({ currentFolder: currentPath });
            const subDirCount = await scanDirectory(entry, directoryId, currentPath, progressCallback);
            count.added += subDirCount.added;
            count.updated += subDirCount.updated;
            count.errors += subDirCount.errors;
            // filesProcessed is handled within the recursive call
        }
    }
    return count;
}


// --- Worker Event Listener ---

self.onmessage = async (event: MessageEvent<ScanRequestMessage>) => {
    if (event.data.type === 'SCAN_FOLDERS') {
        const startTime = performance.now();
        let overallAdded = 0;
        let overallUpdated = 0;
        let overallErrors = 0;
        let overallProcessed = 0; // Total files processed across all folders

        try {
            const directoryIds = event.data.directoryIds;
            let dirsToScan: StoredDirectoryHandle[];

            if (directoryIds && directoryIds.length > 0) {
                // Scan specific directories
                const results = await Promise.all(directoryIds.map(id => db.getTrackedDirectory(id)));
                dirsToScan = results.filter(dir => dir !== undefined) as StoredDirectoryHandle[];
                if (dirsToScan.length !== directoryIds.length) {
                    console.warn("Some requested directory IDs were not found in the database.");
                    // TODO: Report missing IDs back?
                }
            } else {
                // Scan all tracked directories
                dirsToScan = await db.getAllActiveTrackedDirectories(); // Correct method name
            }

            if (dirsToScan.length === 0) {
                postMessage({ type: 'SCAN_COMPLETE', added: 0, updated: 0, errors: 0, totalDuration: 0 });
                return;
            }

            // Initialize progress tracking
            let currentProgress = { processed: 0, total: 0 };
            const progressCallback = (update: Partial<ScanProgressMessage>) => {
                if (update.processed !== undefined) currentProgress.processed = update.processed;
                if (update.total !== undefined) currentProgress.total = update.total;
                postMessage({
                    type: 'SCAN_PROGRESS',
                    ...currentProgress,
                    currentFolder: update.currentFolder, // Pass through current folder
                    error: update.error // Pass through specific file error
                });
            };
            // Attach counters to the callback function itself to manage state across async calls
            (progressCallback as any).processed = 0;
            (progressCallback as any).total = 0;

            // --- Mark existing tracks in scanned directories as hidden initially ---
            console.log("Marking existing tracks as hidden before scan...");
            const dirIdsToScan = dirsToScan.map(d => d.id);
            // Efficiently find all track IDs starting with any of the directory IDs
            const tracksToMark = await db.tracks
                .where('id')
                .startsWithAnyOf(dirIdsToScan.map(id => `${id}:`)) // Find tracks belonging to these dirs
                .primaryKeys(); // Get only the primary keys (IDs)

            if (tracksToMark.length > 0) {
                await db.tracks.bulkUpdate(
                    tracksToMark.map(id => ({ key: id, changes: { isHidden: 1 } }))
                );
                console.log(`Marked ${tracksToMark.length} tracks as potentially hidden.`);
            }
            // --- End marking hidden ---


            for (const dir of dirsToScan) {
                console.log(`Scanning directory: ${dir.name} (ID: ${dir.id})`);
                postMessage({ type: 'SCAN_PROGRESS', ...currentProgress, currentFolder: dir.name });

                // Reset total for each top-level directory scan for accurate progress within that dir
                (progressCallback as any).total = 0;

                const result = await scanDirectory(dir.handle, dir.id, '', progressCallback);
                overallAdded += result.added;
                overallUpdated += result.updated;
                overallErrors += result.errors;
                overallProcessed += result.filesProcessed; // Accumulate processed files
            }

            const endTime = performance.now();
            const totalDuration = (endTime - startTime) / 1000; // Duration in seconds

            console.log(`Scan complete. Added: ${overallAdded}, Updated: ${overallUpdated}, Errors: ${overallErrors}, Total Processed: ${overallProcessed}, Duration: ${totalDuration.toFixed(2)}s`);
            postMessage({
                type: 'SCAN_COMPLETE',
                added: overallAdded,
                updated: overallUpdated,
                errors: overallErrors,
                totalDuration: totalDuration
            });

        } catch (error: any) {
            console.error("Fatal error during scan:", error);
            postMessage({ type: 'SCAN_PROGRESS', error: `Fatal scan error: ${error.message}` });
            // Still send SCAN_COMPLETE but indicate failure via errors
             postMessage({
                type: 'SCAN_COMPLETE',
                added: overallAdded,
                updated: overallUpdated,
                errors: overallErrors + 1, // Increment error count for the fatal error
                totalDuration: (performance.now() - startTime) / 1000
            });
        }
    }
};

console.log("Library Scanner Worker initialized.");

// Optional: Signal readiness
postMessage({ type: 'WORKER_READY' });
