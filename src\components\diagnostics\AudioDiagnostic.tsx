// src/components/AudioDiagnostic.tsx

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { getSharedAudioContext } from '../../services/AudioManager';
import { diagnoseMasterPath, testAudioRouting } from '../../services/AudioRoutingDiagnostics';

interface DiagnosticResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  timestamp: string;
}

export const AudioDiagnostic: React.FC = observer(() => {
  const rootStore = useStore();
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [testAudioPlaying, setTestAudioPlaying] = useState(false);

  const addResult = (test: string, status: DiagnosticResult['status'], message: string) => {
    const result: DiagnosticResult = {
      test,
      status,
      message,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testAudioContext = async () => {
    addResult('Audio Context', 'info', 'Testing audio context...');

    try {
      const audioContext = getSharedAudioContext();
      addResult('Audio Context', 'info', `State: ${audioContext.state}`);
      addResult('Audio Context', 'info', `Sample Rate: ${audioContext.sampleRate}Hz`);
      addResult('Audio Context', 'info', `Base Latency: ${audioContext.baseLatency}s`);

      if (audioContext.state === 'suspended') {
        addResult('Audio Context', 'warning', 'Audio context is suspended - attempting to resume...');
        await audioContext.resume();
        // @ts-ignore - after the await we expect the state to change
        addResult('Audio Context', audioContext.state === 'running' ? 'pass' : 'fail',
          `Resume attempt: ${audioContext.state}`);
      } else {
        addResult('Audio Context', 'pass', 'Audio context is running');
      }
    } catch (error) {
      addResult('Audio Context', 'fail', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const testAudioRoutingManager = async () => {
    addResult('Audio Routing', 'info', 'Testing AudioRoutingManager...');

    try {
      const manager = rootStore.audioRoutingManager;
      addResult('Audio Routing', 'pass', 'AudioRoutingManager instance exists');

      // Test initialization
      await manager.initialize();
      addResult('Audio Routing', 'pass', 'AudioRoutingManager initialized');

      // Test master path
      const masterPath = manager.getMasterPath();
      addResult('Audio Routing', 'pass', 'Master path accessible');

      // Test master analyzer
      const analyzer = masterPath.getMasterAnalyzer();
      analyzer.startAnalysis();
      addResult('Audio Routing', 'pass', 'Master analyzer started');

      // Test deck paths
      const deckCount = rootStore.decks.length;
      addResult('Audio Routing', 'info', `Found ${deckCount} decks`);

      for (const deck of rootStore.decks) {
        const deckPath = await manager.getDeckPath(deck.id);
        if (deckPath) {
          addResult('Audio Routing', 'pass', `Deck ${deck.id} path exists`);
        } else {
          addResult('Audio Routing', 'fail', `Deck ${deck.id} path missing`);
        }
      }

    } catch (error) {
      addResult('Audio Routing', 'fail', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const testMasterPathConnection = () => {
    addResult('Master Connection', 'info', 'Testing master path audio connection...');

    try {
      const masterPath = rootStore.audioRoutingManager.getMasterPath();

      // Check if master path is connected to audio destination
      addResult('Master Connection', 'info', 'Checking master path output connection...');

      // Test master volume control
      masterPath.setMasterVolume(0.8);
      addResult('Master Connection', 'pass', 'Master volume control working');

      // Test analyzer levels
      const analyzer = masterPath.getMasterAnalyzer();
      const rms = analyzer.getRMS();
      const peak = analyzer.getPeak();

      addResult('Master Connection', 'info', `Current RMS: ${rms.toFixed(4)}`);
      addResult('Master Connection', 'info', `Current Peak: ${peak.toFixed(4)}`);

      if (rms === 0 && peak === 0) {
        addResult('Master Connection', 'warning', 'No audio signal detected at master output');
      } else {
        addResult('Master Connection', 'pass', 'Audio signal detected at master output');
      }

    } catch (error) {
      addResult('Master Connection', 'fail', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const generateTestTone = async () => {
    addResult('Test Tone', 'info', 'Generating test tone...');

    try {
      const audioContext = getSharedAudioContext();

      // Create oscillator for test tone
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.frequency.value = 440; // A4 note
      oscillator.type = 'sine';

      gainNode.gain.value = 0.1; // Low volume

      // Connect directly to destination for testing
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Play for 2 seconds
      oscillator.start();
      oscillator.stop(audioContext.currentTime + 2);

      setTestAudioPlaying(true);
      setTimeout(() => setTestAudioPlaying(false), 2000);

      addResult('Test Tone', 'pass', 'Test tone generated (direct to destination)');

    } catch (error) {
      addResult('Test Tone', 'fail', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const testToneToMaster = async () => {
    addResult('Test Tone to Master', 'info', 'Generating test tone through master path...');

    try {
      // First run diagnostics
      diagnoseMasterPath(rootStore.audioRoutingManager);

      await testAudioRouting(rootStore.audioRoutingManager);

      setTestAudioPlaying(true);
      setTimeout(() => setTestAudioPlaying(false), 1000);

      addResult('Test Tone to Master', 'pass', 'Test tone generated through master path');

    } catch (error) {
      addResult('Test Tone to Master', 'fail', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const testDeckAudioEngines = () => {
    addResult('Deck Engines', 'info', 'Testing deck audio engines...');

    try {
      for (const deck of rootStore.decks) {
        if (deck.audioEngine) {
          addResult('Deck Engines', 'pass', `Deck ${deck.id} has audio engine`);

          if (deck.loadedTrack) {
            addResult('Deck Engines', 'info', `Deck ${deck.id} has loaded track: ${deck.loadedTrack.filename}`);

            if (deck.audioEngine.isBufferLoaded()) {
              addResult('Deck Engines', 'pass', `Deck ${deck.id} audio buffer loaded`);
            } else {
              addResult('Deck Engines', 'warning', `Deck ${deck.id} audio buffer not loaded`);
            }

            if (deck.isPlaying) {
              addResult('Deck Engines', 'info', `Deck ${deck.id} is currently playing`);
            } else {
              addResult('Deck Engines', 'info', `Deck ${deck.id} is paused`);
            }
          } else {
            addResult('Deck Engines', 'info', `Deck ${deck.id} has no loaded track`);
          }
        } else {
          addResult('Deck Engines', 'fail', `Deck ${deck.id} missing audio engine`);
        }
      }
    } catch (error) {
      addResult('Deck Engines', 'fail', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const runFullDiagnostic = async () => {
    setIsRunning(true);
    clearResults();

    addResult('Diagnostic', 'info', 'Starting comprehensive audio diagnostic...');

    await testAudioContext();
    await testAudioRoutingManager();
    testMasterPathConnection();
    testDeckAudioEngines();

    addResult('Diagnostic', 'info', 'Diagnostic complete');
    setIsRunning(false);
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass': return '✅';
      case 'fail': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '';
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass': return 'text-green-600';
      case 'fail': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      case 'info': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Card className="p-6 max-w-4xl mx-auto">
      <div className="space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Audio System Diagnostic</h2>
          <p className="text-muted-foreground">
            Comprehensive testing of the audio routing system
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            onClick={runFullDiagnostic}
            disabled={isRunning}
            variant="default"
          >
            {isRunning ? "Running..." : "Run Full Diagnostic"}
          </Button>

          <Button
            onClick={generateTestTone}
            disabled={testAudioPlaying}
            variant="outline"
          >
            {testAudioPlaying ? "Playing..." : "Test Tone (Direct)"}
          </Button>

          <Button
            onClick={testToneToMaster}
            disabled={testAudioPlaying}
            variant="outline"
          >
            {testAudioPlaying ? "Playing..." : "Test Tone (Master)"}
          </Button>

          <Button
            onClick={clearResults}
            variant="outline"
            size="sm"
          >
            Clear Results
          </Button>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Diagnostic Results:</Label>
          <div className="bg-gray-50 border rounded p-3 h-96 overflow-y-auto">
            {results.length === 0 ? (
              <p className="text-muted-foreground text-sm">No diagnostic results yet...</p>
            ) : (
              <div className="space-y-1">
                {results.map((result, index) => (
                  <div key={index} className="text-sm font-mono flex items-start gap-2">
                    <span className="text-xs text-gray-500 min-w-[80px]">
                      {result.timestamp}
                    </span>
                    <span className="min-w-[20px]">
                      {getStatusIcon(result.status)}
                    </span>
                    <span className="font-medium min-w-[120px]">
                      {result.test}:
                    </span>
                    <span className={getStatusColor(result.status)}>
                      {result.message}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
});

export default AudioDiagnostic;
