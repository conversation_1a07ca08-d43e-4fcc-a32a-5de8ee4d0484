import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/contexts/StoreContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Crown, Radio } from 'lucide-react';

const MasterDeckControls: React.FC = observer(() => {
  const { settingsStore, syncStore, decks } = useStore();

  const handleMasterDeckSelect = (deckId: string) => {
    // If clicking the current master, deselect it (go to ghost master)
    if (syncStore.masterDeckId === deckId) {
      syncStore.setMasterDeck(null);
    } else {
      syncStore.setMasterDeck(deckId);
      // Disable auto master mode when manually selecting
      if (settingsStore.autoMasterMode) {
        settingsStore.setAutoMasterMode(false);
      }
    }
  };

  const handleAutoMasterToggle = (enabled: boolean) => {
    settingsStore.setAutoMasterMode(enabled);
    
    // If enabling auto master mode, trigger auto selection
    if (enabled) {
      syncStore.updateAutoMaster();
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Crown className="h-5 w-5" />
          Master Deck Control
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Auto Master Mode Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Radio className="h-4 w-4" />
            <Label htmlFor="auto-master-mode" className="text-sm font-medium">
              Auto Master Mode
            </Label>
          </div>
          <Switch
            id="auto-master-mode"
            checked={settingsStore.autoMasterMode}
            onCheckedChange={handleAutoMasterToggle}
          />
        </div>

        {/* Current Master Status */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Current Master:</div>
          <div className="flex items-center gap-2">
            {syncStore.masterDeckId ? (
              <Badge variant="default" className="flex items-center gap-1">
                <Crown className="h-3 w-3" />
                Deck {syncStore.masterDeckId.replace('deck-', '')}
              </Badge>
            ) : (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Radio className="h-3 w-3" />
                Ghost Master ({syncStore.ghostMasterBpm.toFixed(1)} BPM)
              </Badge>
            )}
          </div>
        </div>

        {/* Manual Master Selection */}
        {!settingsStore.autoMasterMode && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Manual Selection:</div>
            <div className="grid grid-cols-2 gap-2">
              {decks.map((deck) => {
                const isMaster = syncStore.masterDeckId === deck.id;
                const isPlaying = deck.isPlaying;
                const hasTrack = !!deck.loadedTrack;
                
                return (
                  <Button
                    key={deck.id}
                    variant={isMaster ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleMasterDeckSelect(deck.id)}
                    disabled={!hasTrack}
                    className="flex items-center gap-2"
                  >
                    {isMaster && <Crown className="h-3 w-3" />}
                    Deck {deck.id.replace('deck-', '')}
                    {isPlaying && (
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    )}
                  </Button>
                );
              })}
            </div>
          </div>
        )}

        {/* Auto Master Mode Info */}
        {settingsStore.autoMasterMode && (
          <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
            <div className="font-medium mb-1">Auto Master Rules:</div>
            <ul className="space-y-1">
              <li>• First playing deck becomes master</li>
              <li>• Master switches when track ends</li>
              <li>• Highest audio level takes priority</li>
              <li>• Requires valid BPM/beatgrid data</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

export default MasterDeckControls;
