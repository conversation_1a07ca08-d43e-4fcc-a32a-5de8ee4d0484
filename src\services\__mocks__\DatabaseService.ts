// src/services/__mocks__/DatabaseService.ts

// This will be the type for our mock database store
type MockDbStore = { [key: string]: any };

// In-memory store for settings
const mockSettingsDb: MockDbStore = {};

// In-memory store for deck states (keyed by deckId, e.g., 'deckState_deck-1')
const mockDeckStateDb: MockDbStore = {};

const db = {
  // Settings methods
  saveSetting: jest.fn(async (setting: { key: string; value: any }) => {
    console.log(`MockDB: Saving setting ${setting.key}`, setting.value);
    mockSettingsDb[setting.key] = setting.value;
    return Promise.resolve();
  }),
  getSetting: jest.fn(async (key: string) => {
    console.log(`MockDB: Getting setting ${key}`, mockSettingsDb[key]);
    return Promise.resolve(mockSettingsDb[key]);
  }),
  getAllSettings: jest.fn(async () => {
    console.log('MockDB: Getting all settings', mockSettingsDb);
    return Promise.resolve(
      Object.entries(mockSettingsDb).map(([key, value]) => ({ key, value }))
    );
  }),
  deleteSetting: jest.fn(async (key: string) => {
    delete mockSettingsDb[key];
    return Promise.resolve();
  }),

  // Deck state methods (simplified for testing)
  saveDeckState: jest.fn(async (deckId: string, state: any) => {
    console.log(`MockDB: Saving deck state for ${deckId}`, state);
    mockDeckStateDb[`deckState_${deckId}`] = state;
    return Promise.resolve();
  }),
  loadDeckState: jest.fn(async (deckId: string) => {
    console.log(`MockDB: Loading deck state for ${deckId}`, mockDeckStateDb[`deckState_${deckId}`]);
    return Promise.resolve(mockDeckStateDb[`deckState_${deckId}`] || null);
  }),
  deleteDeckState: jest.fn(async (deckId: string) => {
    delete mockDeckStateDb[`deckState_${deckId}`];
    return Promise.resolve();
  }),

  // Track methods (not directly used by these store tests but good to have for completeness)
  saveTrack: jest.fn(async (track) => Promise.resolve()),
  getTrack: jest.fn(async (id) => Promise.resolve(undefined)),
  getAllTracks: jest.fn(async () => Promise.resolve([])),
  deleteTrack: jest.fn(async (id) => Promise.resolve()),
  updateTrack: jest.fn(async (id, updates) => Promise.resolve()),

  // Directory methods
  saveDirectory: jest.fn(async (directory) => Promise.resolve()),
  getDirectory: jest.fn(async (id) => Promise.resolve(undefined)),
  getAllDirectories: jest.fn(async () => Promise.resolve([])),
  deleteDirectory: jest.fn(async (id) => Promise.resolve()),
  updateDirectory: jest.fn(async (id, updates) => Promise.resolve()),
  
  // Method to clear the mock database for test isolation
  clearAllMockData: jest.fn(() => {
    for (const key in mockSettingsDb) {
      delete mockSettingsDb[key];
    }
    for (const key in mockDeckStateDb) {
      delete mockDeckStateDb[key];
    }
    console.log('MockDB: All data cleared');
  }),
};

// Export AppSetting type if needed for other mocks, though not strictly for this one
export interface AppSetting {
  key: string;
  value: any;
}

export { db };
