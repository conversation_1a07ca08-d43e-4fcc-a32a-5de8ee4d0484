import React from 'react';
import { observer } from 'mobx-react-lite';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import TrackInfoDisplay from './TrackInfoDisplay';
import OverviewWaveformComponent from './OverviewWaveformComponent';
import AudioEngineTest from './AudioEngineTest';
import SyncButton from './SyncButton';
import NudgeControls from './NudgeControls';

interface DeckComponentProps {
  deck: DeckStoreInstance;
}

const DeckComponent: React.FC<DeckComponentProps> = observer(({ deck }) => {
  // if deck's state is not yet loaded return a loading... screen
  if (!deck.stateLoaded) return <div>Loading...</div>;

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Deck {deck.id.replace('deck-', '')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Track Info Display */}
        <TrackInfoDisplay deck={deck} />

        {/* Waveform Overview */}
        {/* <div className="space-y-1">
          <div className="text-xs text-muted-foreground">Overview</div>
          <OverviewWaveformComponent
            deck={deck}
            height={60}
            waveformStyle="frequency"
          />
        </div> */}

        {/* Detailed Waveform View */}
        {/* <div className="space-y-1">
          <div className="flex justify-between items-center">
            <div className="text-xs text-muted-foreground">Detailed View</div>
          </div>
          <DetailedWaveformComponent
            deck={deck}
            height={170}
            showTimeline={true}
            showBeatgrid={true}
            timeOffset={deck.timeOffset > 0 ? deck.timeOffset : undefined}
            bpm={deck.currentBpm > 0 ? deck.currentBpm : undefined}
            showSegments={!!deck.segments.length}
            segments={deck.segments}
            zoomable={true}
          />
        </div> */}

        {/* Filter Control */}
        {/* <div className="flex justify-center py-2">
          <FilterControl deck={deck} />
        </div> */}

        {/* Sync Controls */}
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">Sync & Master</div>
          <div className="grid grid-cols-2 gap-4">
            <SyncButton deck={deck} />
            <NudgeControls deck={deck} />
          </div>
        </div>

        {/* Audio Engine Test */}
        <AudioEngineTest deck={deck} />
      </CardContent>
    </Card>
  );
});

export default DeckComponent;

