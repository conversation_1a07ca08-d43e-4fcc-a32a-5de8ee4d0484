interface BeatDetectorWorkerConfig {
    sampleRate: number; // This will be updated per analysis
    round: boolean;
    float: number;
    bpmRange: [number, number];
    timeSignature: number;
}

interface BeatDetectorAnalyzeMessage {
    action: 'analyze';
    audioData: Float32Array; // Mono audio data
    sampleRate: number;
    trackId: string;
}

interface BeatDetectorInitMessage {
    action: 'init';
}

type BeatDetectorReceivedMessage = BeatDetectorAnalyzeMessage | BeatDetectorInitMessage;

interface Peak {
    position: number;
    volume: number;
}

interface IntervalGroup {
    tempo: number;
    count: number;
    position: number; // Position of the first peak in the group
    peaks: Peak[];
}

interface BeatDetectorWorkerResult {
    trackId: string;
    peaks?: Peak[];
    offset?: number;
    firstBar?: number;
    bpm?: number;
    error?: string;
}

// Default configuration, similar to BeatDetect.js defaults
const config: BeatDetectorWorkerConfig = {
    sampleRate: 44100,
    round: false,
    float: 8,
    bpmRange: [90, 180],
    timeSignature: 4,
};

function _floatRound(value: number, precision: number): number {
    const multiplier = Math.pow(10, precision || 0);
    return Math.round(value * multiplier) / multiplier;
}

function _getPeaks(monoAudioData: Float32Array, currentSampleRate: number): Peak[] {
    // Simulate stereo data as original _getPeaks expects [dataL, dataR]
    const data: [Float32Array, Float32Array] = [monoAudioData, monoAudioData];
    const partSize = currentSampleRate / 4; // Each part is 0.25 seconds
    const numParts = Math.floor(data[0].length / partSize);
    let peaks: Peak[] = [];

    for (let i = 0; i < numParts; ++i) {
        let max: Peak | null = null;
        const startSample = i * partSize;
        const endSample = (i + 1) * partSize;

        for (let j = startSample; j < endSample; ++j) {
            // Since data[0] and data[1] are the same (monoAudioData),
            // Math.abs(data[0][j]) is sufficient.
            const volume = Math.abs(data[0][j]);
            if (!max || (volume > max.volume)) {
                max = {
                    position: j,
                    volume: volume
                };
            }
        }
        if (max) {
            peaks.push(max);
        }
    }

    peaks.sort((a, b) => b.volume - a.volume);
    peaks = peaks.splice(0, Math.ceil(peaks.length * 0.5)); // Keep louder half
    peaks.sort((a, b) => a.position - b.position); // Sort by position

    return peaks;
}

function _getIntervals(peaks: Peak[], currentSampleRate: number): IntervalGroup[] {
    const groups: IntervalGroup[] = [];
    peaks.forEach((peak, index) => {
        for (let i = 1; (index + i) < peaks.length && i < 10; ++i) {
            const groupTempo = (60 * currentSampleRate) / (peaks[index + i].position - peak.position);
            let tempo = groupTempo;

            while (tempo <= config.bpmRange[0]) {
                tempo *= 2;
            }
            while (tempo > config.bpmRange[1]) {
                tempo /= 2;
            }

            if (config.round === true) {
                tempo = Math.round(tempo);
            } else {
                tempo = _floatRound(tempo, config.float);
            }

            const existingGroup = groups.find(g => g.tempo === tempo);
            if (existingGroup) {
                existingGroup.peaks.push(peak);
                existingGroup.count++;
            } else {
                groups.push({
                    tempo: tempo,
                    count: 1,
                    position: peak.position,
                    peaks: [peak]
                });
            }
        }
    });
    return groups;
}

function _getLowestTimeOffset(position: number, bpm: number, currentSampleRate: number): number {
    const bpmTime = 60 / bpm; // Duration of one beat in seconds
    const firstBeatTime = position / currentSampleRate; // Time of the peak in seconds
    let offset = firstBeatTime;

    // Rewind by full measures (timeSignature * bpmTime)
    while (offset >= (bpmTime * config.timeSignature)) {
        offset -= (bpmTime * config.timeSignature);
    }
    // If still too large, rewind by single beats
    while (offset >= bpmTime) {
         offset -= bpmTime;
    }

    // Ensure offset is not negative
    if (offset < 0) {
        // This case might indicate an issue or a very early beat.
        // The original logic had a loop `while (offset < 0) { offset += bpmTime; }`
        // which could be problematic if the initial offset is very negative.
        // A simpler approach for negative offsets, assuming they are small:
        offset = (offset % bpmTime + bpmTime) % bpmTime;
    }
    return offset;
}

function _getOffsets(monoAudioData: Float32Array, bpm: number, currentSampleRate: number): { offset: number, firstBar: number } {
    const partSize = currentSampleRate / 2; // 0.5 seconds
    const numParts = Math.floor(monoAudioData.length / partSize);
    // Limit analysis for offsets to a reasonable duration, e.g., first 60 parts (30 seconds)
    // or fewer if the track is shorter.
    const partsToAnalyze = Math.min(numParts, 60);
    let tempPeaks: Peak[] = [];

    for (let i = 0; i < partsToAnalyze; ++i) {
        let max: Peak | null = null;
        const startSample = i * partSize;
        const endSample = (i + 1) * partSize;
        for (let j = startSample; j < endSample; ++j) {
            const volume = monoAudioData[j]; // No Math.abs here in original _getOffsets
            if (!max || (volume > max.volume)) {
                max = {
                    // Original offset: j - Math.round(((60 / bpm) * 0.05) * currentSampleRate)
                    // This 5% offset aims to find the start of the beat, not its peak.
                    position: j - Math.round(((60 / bpm) * 0.05) * currentSampleRate),
                    volume: volume
                };
            }
        }
        if (max) {
            tempPeaks.push(max);
        }
    }

    const unsortedPeaks = [...tempPeaks];
    tempPeaks.sort((a, b) => b.volume - a.volume); // Sort by decreasing volume

    if (tempPeaks.length === 0) {
        console.warn("BeatDetector Worker: No peaks found for offset calculation.");
        return { offset: 0, firstBar: 0 };
    }
    
    const refPeak = tempPeaks[0];
    // Ensure refPeak.position is not negative after the 5% adjustment
    const refPeakPosition = Math.max(0, refPeak.position);
    const refOffset = _getLowestTimeOffset(refPeakPosition, bpm, currentSampleRate);

    let mean = 0;
    let divider = 0;
    for (let i = 0; i < tempPeaks.length; ++i) {
        const peakPosition = Math.max(0, tempPeaks[i].position);
        const offset = _getLowestTimeOffset(peakPosition, bpm, currentSampleRate);
        // Original condition: offset - refOffset < 0.05 || refOffset - offset > -0.05
        // Simplified: Math.abs(offset - refOffset) < 0.05
        if (Math.abs(offset - refOffset) < 0.05) {
            mean += offset;
            divider++;
        }
    }
    const calculatedOffset = divider > 0 ? mean / divider : refOffset; // Fallback to refOffset if no similar offsets found

    let firstBarPeakIndex = 0;
    // Threshold is arbitrary (0.02 in original)
    while (firstBarPeakIndex < unsortedPeaks.length && unsortedPeaks[firstBarPeakIndex].volume < 0.02) {
        firstBarPeakIndex++;
    }

    let firstBarTime = 0;
    if (firstBarPeakIndex < unsortedPeaks.length && unsortedPeaks[firstBarPeakIndex].position >= 0) {
        firstBarTime = unsortedPeaks[firstBarPeakIndex].position / currentSampleRate;
    } else if (unsortedPeaks.length > 0 && unsortedPeaks[0].position >=0) {
        // Fallback if no peak meets threshold, use the first detected peak's time
        firstBarTime = unsortedPeaks[0].position / currentSampleRate;
    }


    // Original logic for adjusting firstBarTime
    const beatDuration = 60 / bpm;
    if (firstBarTime > calculatedOffset && firstBarTime < beatDuration) {
         // This condition seems to imply if firstBarTime is plausible and between offset and a beat later
         // it might be adjusted. The original was:
         // if (firstBar > (mean / divider) && firstBar < (60 / bpm)) { firstBar = (mean / divider) }
         // Let's refine: if firstBarTime is significantly after calculatedOffset but within the first beat,
         // it might be more accurate to use calculatedOffset.
         // However, for now, let's keep it simpler: use the detected firstBarTime unless it's clearly problematic.
    } else if (firstBarTime === 0 && calculatedOffset > 0) {
        // If no suitable firstBarTime was found, but we have a calculatedOffset
        firstBarTime = calculatedOffset;
    }


    return {
        offset: calculatedOffset,
        firstBar: firstBarTime
    };
}

function analyze(audioData: Float32Array, sampleRate: number, trackId: string): BeatDetectorWorkerResult {
    console.log(`BeatDetector Worker: Starting analysis for TrackID: ${trackId}, SampleRate: ${sampleRate}`);
    config.sampleRate = sampleRate; // Update current sample rate for this analysis

    // Helper function to convert peak positions to seconds for the output
    const convertPeaksToSeconds = (peaksToConvert: Peak[]): Peak[] => {
        if (!peaksToConvert) return [];
        return peaksToConvert.map(peak => ({
            ...peak,
            position: _floatRound(peak.position / sampleRate, config.float)
        }));
    };

    const allPeaks = _getPeaks(audioData, sampleRate);
    if (!allPeaks || allPeaks.length === 0) {
        return { trackId, error: "No peaks found by BeatDetector." };
    }

    const groups = _getIntervals(allPeaks, sampleRate);
    if (!groups || groups.length === 0) {
        return {
            trackId,
            peaks: convertPeaksToSeconds(allPeaks),
            error: "No beat intervals found by BeatDetector."
        };
    }

    const top = groups.sort((intA, intB) => intB.count - intA.count).slice(0, 5);
    if (top.length === 0) {
        return {
            trackId,
            peaks: convertPeaksToSeconds(allPeaks),
            error: "No significant beat intervals found after sorting by BeatDetector."
        };
    }

    const bestTempo = top[0].tempo;
    const { offset, firstBar } = _getOffsets(audioData, bestTempo, sampleRate);
    return {
        trackId,
        peaks: convertPeaksToSeconds(allPeaks),
        offset: _floatRound(offset, config.float),
        firstBar: _floatRound(firstBar, config.float),
        bpm: _floatRound(bestTempo, config.float),
    };
}

self.onmessage = (event: MessageEvent<BeatDetectorReceivedMessage>) => {
    const data = event.data;

    if (data.action === 'init') {
        console.log('BeatDetector Worker: Initialized.');
        self.postMessage({ status: 'ready', worker: 'BeatDetectorWorker' });
    } else if (data.action === 'analyze') {
        try {
            console.time('BeatDetector Worker'); // Start a timer
            const result = analyze(data.audioData, data.sampleRate, data.trackId);
            console.timeEnd('BeatDetector Worker'); // End the timer started in audioAnalyzer.worker.ts
            self.postMessage(result);
        } catch (e: any) {
            console.error(`BeatDetector Worker: Error during analysis for track ${data.trackId}:`, e);
            self.postMessage({
                trackId: data.trackId,
                error: `BeatDetector analysis failed: ${e.message || e}`
            });
        }
    }
};

export { }; // Make this a module
