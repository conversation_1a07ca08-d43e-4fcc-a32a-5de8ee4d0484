import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';
import eslintConfigPrettier from 'eslint-config-prettier'; // Import prettier config
import mobxPlugin from 'eslint-plugin-mobx'; // Import mobx plugin

export default tseslint.config(
  { ignores: ['dist'] },
  {
    // Apply prettier config last
    extends: [js.configs.recommended, ...tseslint.configs.recommended, eslintConfigPrettier],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'mobx': mobxPlugin, // Add mobx plugin
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      ...mobxPlugin.configs.recommended.rules, // Add mobx recommended rules
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  },
)
