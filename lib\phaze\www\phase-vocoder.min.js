!function t(e,s,r){function i(o,f){if(!s[o]){if(!e[o]){var h="function"==typeof require&&require;if(!f&&h)return h(o,!0);if(n)return n(o,!0);var u=new Error("Cannot find module '"+o+"'");throw u.code="MODULE_NOT_FOUND",u}var a=s[o]={exports:{}};e[o][0].call(a.exports,(function(t){return i(e[o][1][t]||t)}),a,a.exports,t,e,s,r)}return s[o].exports}for(var n="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(t,e,s){"use strict";function r(t){if(this.size=0|t,this.size<=1||0!=(this.size&this.size-1))throw new Error("FFT size must be a power of two and bigger than 1");this._csize=t<<1;for(var e=new Array(2*this.size),s=0;s<e.length;s+=2){const t=Math.PI*s/this.size;e[s]=Math.cos(t),e[s+1]=-Math.sin(t)}this.table=e;for(var r=0,i=1;this.size>i;i<<=1)r++;this._width=r%2==0?r-1:r,this._bitrev=new Array(1<<this._width);for(var n=0;n<this._bitrev.length;n++){this._bitrev[n]=0;for(var o=0;o<this._width;o+=2){var f=this._width-o-2;this._bitrev[n]|=(n>>>o&3)<<f}}this._out=null,this._data=null,this._inv=0}e.exports=r,r.prototype.fromComplexArray=function(t,e){for(var s=e||new Array(t.length>>>1),r=0;r<t.length;r+=2)s[r>>>1]=t[r];return s},r.prototype.createComplexArray=function(){const t=new Array(this._csize);for(var e=0;e<t.length;e++)t[e]=0;return t},r.prototype.toComplexArray=function(t,e){for(var s=e||this.createComplexArray(),r=0;r<s.length;r+=2)s[r]=t[r>>>1],s[r+1]=0;return s},r.prototype.completeSpectrum=function(t){for(var e=this._csize,s=e>>>1,r=2;r<s;r+=2)t[e-r]=t[r],t[e-r+1]=-t[r+1]},r.prototype.transform=function(t,e){if(t===e)throw new Error("Input and output buffers must be different");this._out=t,this._data=e,this._inv=0,this._transform4(),this._out=null,this._data=null},r.prototype.realTransform=function(t,e){if(t===e)throw new Error("Input and output buffers must be different");this._out=t,this._data=e,this._inv=0,this._realTransform4(),this._out=null,this._data=null},r.prototype.inverseTransform=function(t,e){if(t===e)throw new Error("Input and output buffers must be different");this._out=t,this._data=e,this._inv=1,this._transform4();for(var s=0;s<t.length;s++)t[s]/=this.size;this._out=null,this._data=null},r.prototype._transform4=function(){var t,e,s=this._out,r=this._csize,i=1<<this._width,n=r/i<<1,o=this._bitrev;if(4===n)for(t=0,e=0;t<r;t+=n,e++){const s=o[e];this._singleTransform2(t,s,i)}else for(t=0,e=0;t<r;t+=n,e++){const s=o[e];this._singleTransform4(t,s,i)}var f=this._inv?-1:1,h=this.table;for(i>>=2;i>=2;i>>=2){var u=(n=r/i<<1)>>>2;for(t=0;t<r;t+=n)for(var a=t+u,p=t,l=0;p<a;p+=2,l+=i){const t=p,e=t+u,r=e+u,i=r+u,n=s[t],o=s[t+1],a=s[e],c=s[e+1],d=s[r],v=s[r+1],m=s[i],b=s[i+1],_=n,B=o,g=h[l],y=f*h[l+1],w=a*g-c*y,S=a*y+c*g,z=h[2*l],x=f*h[2*l+1],C=d*z-v*x,I=d*x+v*z,k=h[3*l],A=f*h[3*l+1],T=m*k-b*A,O=m*A+b*k,q=_+C,P=B+I,M=_-C,F=B-I,R=w+T,W=S+O,H=f*(w-T),E=f*(S-O),N=q+R,j=P+W,L=q-R,D=P-W,U=M+E,V=F-H,G=M-E,J=F+H;s[t]=N,s[t+1]=j,s[e]=U,s[e+1]=V,s[r]=L,s[r+1]=D,s[i]=G,s[i+1]=J}}},r.prototype._singleTransform2=function(t,e,s){const r=this._out,i=this._data,n=i[e],o=i[e+1],f=i[e+s],h=i[e+s+1],u=n+f,a=o+h,p=n-f,l=o-h;r[t]=u,r[t+1]=a,r[t+2]=p,r[t+3]=l},r.prototype._singleTransform4=function(t,e,s){const r=this._out,i=this._data,n=this._inv?-1:1,o=2*s,f=3*s,h=i[e],u=i[e+1],a=i[e+s],p=i[e+s+1],l=i[e+o],c=i[e+o+1],d=i[e+f],v=i[e+f+1],m=h+l,b=u+c,_=h-l,B=u-c,g=a+d,y=p+v,w=n*(a-d),S=n*(p-v),z=m+g,x=b+y,C=_+S,I=B-w,k=m-g,A=b-y,T=_-S,O=B+w;r[t]=z,r[t+1]=x,r[t+2]=C,r[t+3]=I,r[t+4]=k,r[t+5]=A,r[t+6]=T,r[t+7]=O},r.prototype._realTransform4=function(){var t,e,s=this._out,r=this._csize,i=1<<this._width,n=r/i<<1,o=this._bitrev;if(4===n)for(t=0,e=0;t<r;t+=n,e++){const s=o[e];this._singleRealTransform2(t,s>>>1,i>>>1)}else for(t=0,e=0;t<r;t+=n,e++){const s=o[e];this._singleRealTransform4(t,s>>>1,i>>>1)}var f=this._inv?-1:1,h=this.table;for(i>>=2;i>=2;i>>=2){var u=(n=r/i<<1)>>>1,a=u>>>1,p=a>>>1;for(t=0;t<r;t+=n)for(var l=0,c=0;l<=p;l+=2,c+=i){var d=t+l,v=d+a,m=v+a,b=m+a,_=s[d],B=s[d+1],g=s[v],y=s[v+1],w=s[m],S=s[m+1],z=s[b],x=s[b+1],C=_,I=B,k=h[c],A=f*h[c+1],T=g*k-y*A,O=g*A+y*k,q=h[2*c],P=f*h[2*c+1],M=w*q-S*P,F=w*P+S*q,R=h[3*c],W=f*h[3*c+1],H=z*R-x*W,E=z*W+x*R,N=C+M,j=I+F,L=C-M,D=I-F,U=T+H,V=O+E,G=f*(T-H),J=f*(O-E),K=N+U,Q=j+V,X=L+J,Y=D-G;if(s[d]=K,s[d+1]=Q,s[v]=X,s[v+1]=Y,0!==l){if(l!==p){var Z=L+-f*J,$=-D+-f*G,tt=N+-f*U,et=-j- -f*V,st=t+a-l,rt=t+u-l;s[st]=Z,s[st+1]=$,s[rt]=tt,s[rt+1]=et}}else{var it=N-U,nt=j-V;s[m]=it,s[m+1]=nt}}}},r.prototype._singleRealTransform2=function(t,e,s){const r=this._out,i=this._data,n=i[e],o=i[e+s],f=n+o,h=n-o;r[t]=f,r[t+1]=0,r[t+2]=h,r[t+3]=0},r.prototype._singleRealTransform4=function(t,e,s){const r=this._out,i=this._data,n=this._inv?-1:1,o=2*s,f=3*s,h=i[e],u=i[e+s],a=i[e+o],p=i[e+f],l=h+a,c=h-a,d=u+p,v=n*(u-p),m=l+d,b=c,_=-v,B=l-d,g=c,y=v;r[t]=m,r[t+1]=0,r[t+2]=b,r[t+3]=_,r[t+4]=B,r[t+5]=0,r[t+6]=g,r[t+7]=y}},{}],2:[function(t,e,s){"use strict";const r=128;class i extends AudioWorkletProcessor{constructor(t){super(t),this.nbInputs=t.numberOfInputs,this.nbOutputs=t.numberOfOutputs,this.blockSize=t.processorOptions.blockSize,this.hopSize=r,this.nbOverlaps=this.blockSize/this.hopSize,this.inputBuffers=new Array(this.nbInputs),this.inputBuffersHead=new Array(this.nbInputs),this.inputBuffersToSend=new Array(this.nbInputs);for(var e=0;e<this.nbInputs;e++)this.allocateInputChannels(e,1);this.outputBuffers=new Array(this.nbOutputs),this.outputBuffersToRetrieve=new Array(this.nbOutputs);for(e=0;e<this.nbOutputs;e++)this.allocateOutputChannels(e,1)}reallocateChannelsIfNeeded(t,e){for(var s=0;s<this.nbInputs;s++){let e=t[s].length;e!=this.inputBuffers[s].length&&this.allocateInputChannels(s,e)}for(s=0;s<this.nbOutputs;s++){let t=e[s].length;t!=this.outputBuffers[s].length&&this.allocateOutputChannels(s,t)}}allocateInputChannels(t,e){this.inputBuffers[t]=new Array(e);for(var s=0;s<e;s++)this.inputBuffers[t][s]=new Float32Array(this.blockSize+r),this.inputBuffers[t][s].fill(0);this.inputBuffersHead[t]=new Array(e),this.inputBuffersToSend[t]=new Array(e);for(s=0;s<e;s++)this.inputBuffersHead[t][s]=this.inputBuffers[t][s].subarray(0,this.blockSize),this.inputBuffersToSend[t][s]=new Float32Array(this.blockSize)}allocateOutputChannels(t,e){this.outputBuffers[t]=new Array(e);for(var s=0;s<e;s++)this.outputBuffers[t][s]=new Float32Array(this.blockSize),this.outputBuffers[t][s].fill(0);this.outputBuffersToRetrieve[t]=new Array(e);for(s=0;s<e;s++)this.outputBuffersToRetrieve[t][s]=new Float32Array(this.blockSize),this.outputBuffersToRetrieve[t][s].fill(0)}readInputs(t){if(t[0].length&&0==t[0][0].length)for(var e=0;e<this.nbInputs;e++)for(var s=0;s<this.inputBuffers[e].length;s++)this.inputBuffers[e][s].fill(0,this.blockSize);else for(e=0;e<this.nbInputs;e++)for(s=0;s<this.inputBuffers[e].length;s++){let r=t[e][s];this.inputBuffers[e][s].set(r,this.blockSize)}}writeOutputs(t){for(var e=0;e<this.nbInputs;e++)for(var s=0;s<this.inputBuffers[e].length;s++){let i=this.outputBuffers[e][s].subarray(0,r);t[e][s].set(i)}}shiftInputBuffers(){for(var t=0;t<this.nbInputs;t++)for(var e=0;e<this.inputBuffers[t].length;e++)this.inputBuffers[t][e].copyWithin(0,r)}shiftOutputBuffers(){for(var t=0;t<this.nbOutputs;t++)for(var e=0;e<this.outputBuffers[t].length;e++)this.outputBuffers[t][e].copyWithin(0,r),this.outputBuffers[t][e].subarray(this.blockSize-r).fill(0)}prepareInputBuffersToSend(){for(var t=0;t<this.nbInputs;t++)for(var e=0;e<this.inputBuffers[t].length;e++)this.inputBuffersToSend[t][e].set(this.inputBuffersHead[t][e])}handleOutputBuffersToRetrieve(){for(var t=0;t<this.nbOutputs;t++)for(var e=0;e<this.outputBuffers[t].length;e++)for(var s=0;s<this.blockSize;s++)this.outputBuffers[t][e][s]+=this.outputBuffersToRetrieve[t][e][s]/this.nbOverlaps}process(t,e,s){return this.reallocateChannelsIfNeeded(t,e),this.readInputs(t),this.shiftInputBuffers(),this.prepareInputBuffersToSend(),this.processOLA(this.inputBuffersToSend,this.outputBuffersToRetrieve,s),this.handleOutputBuffersToRetrieve(),this.writeOutputs(e),this.shiftOutputBuffers(),!0}processOLA(t,e,s){console.assert(!1,"Not overriden")}}e.exports=i},{}],3:[function(t,e,s){"use strict";const r=t("./ola-processor.js"),i=t("fft.js");registerProcessor("phase-vocoder-processor",class extends r{static get parameterDescriptors(){return[{name:"pitchFactor",defaultValue:1}]}constructor(t){t.processorOptions={blockSize:2048},super(t),this.fftSize=this.blockSize,this.timeCursor=0,this.hannWindow=function(t){let e=new Float32Array(t);for(var s=0;s<t;s++)e[s]=.5*(1-Math.cos(2*Math.PI*s/t));return e}(this.blockSize),this.fft=new i(this.fftSize),this.freqComplexBuffer=this.fft.createComplexArray(),this.freqComplexBufferShifted=this.fft.createComplexArray(),this.timeComplexBuffer=this.fft.createComplexArray(),this.magnitudes=new Float32Array(this.fftSize/2+1),this.peakIndexes=new Int32Array(this.magnitudes.length),this.nbPeaks=0}processOLA(t,e,s){const r=s.pitchFactor[s.pitchFactor.length-1];for(var i=0;i<this.nbInputs;i++)for(var n=0;n<t[i].length;n++){var o=t[i][n],f=e[i][n];this.applyHannWindow(o),this.fft.realTransform(this.freqComplexBuffer,o),this.computeMagnitudes(),this.findPeaks(),this.shiftPeaks(r),this.fft.completeSpectrum(this.freqComplexBufferShifted),this.fft.inverseTransform(this.timeComplexBuffer,this.freqComplexBufferShifted),this.fft.fromComplexArray(this.timeComplexBuffer,f),this.applyHannWindow(f)}this.timeCursor+=this.hopSize}applyHannWindow(t){for(var e=0;e<this.blockSize;e++)t[e]=t[e]*this.hannWindow[e]}computeMagnitudes(){for(var t=0,e=0;t<this.magnitudes.length;){let s=this.freqComplexBuffer[e],r=this.freqComplexBuffer[e+1];this.magnitudes[t]=s**2+r**2,t+=1,e+=2}}findPeaks(){this.nbPeaks=0;var t=2;let e=this.magnitudes.length-2;for(;t<e;){let e=this.magnitudes[t];this.magnitudes[t-1]>=e||this.magnitudes[t-2]>=e?t++:this.magnitudes[t+1]>=e||this.magnitudes[t+2]>=e?t++:(this.peakIndexes[this.nbPeaks]=t,this.nbPeaks++,t+=2)}}shiftPeaks(t){this.freqComplexBufferShifted.fill(0);for(var e=0;e<this.nbPeaks;e++){let n=this.peakIndexes[e],o=Math.round(n*t);if(o>this.magnitudes.length)break;var s=0,r=this.fftSize;if(e>0){let t=this.peakIndexes[e-1];s=n-Math.floor((n-t)/2)}if(e<this.nbPeaks-1){let t=this.peakIndexes[e+1];r=n+Math.ceil((t-n)/2)}let f=r-n;for(var i=s-n;i<f;i++){let t=n+i,e=o+i;if(e>=this.magnitudes.length)break;let s=2*Math.PI*(e-t)/this.fftSize,r=Math.cos(s*this.timeCursor),f=Math.sin(s*this.timeCursor),h=2*t,u=h+1,a=this.freqComplexBuffer[h],p=this.freqComplexBuffer[u],l=a*r-p*f,c=a*f+p*r,d=2*e,v=d+1;this.freqComplexBufferShifted[d]+=l,this.freqComplexBufferShifted[v]+=c}}}})},{"./ola-processor.js":2,"fft.js":1}]},{},[3]);