import { useEffect, useRef } from 'react';

// Define a type for the props object. It can be any object with string keys.
type Props = Record<string, any>;

// Define the structure of the changes object that will be logged
type ChangesObject<T extends Props> = {
  [K in keyof T]?: {
    from: T[K];
    to: T[K];
  };
};

/**
 * A custom hook that logs to the console the props that have changed between renders.
 * Useful for debugging why a component or another hook is re-rendering.
 *
 * @param name - A string name to identify the component/hook in the logs.
 * @param props - An object containing the props/values to track for changes.
 */
function useWhyDidYouUpdate<T extends Props>(name: string, props: T): void {
  // Use a ref to store the previous props
  const previousPropsRef = useRef<T>(undefined);

  useEffect(() => {
    if (previousPropsRef.current !== undefined) {
      // Get the previous props from the ref
      const previousProps = previousPropsRef.current;

      // Collect all keys from both previous and current props
      const allKeys = Object.keys({ ...previousProps, ...props }) as Array<keyof T>;

      // Object to store the differences
      const changes: ChangesObject<T> = {};
      let hasChanges = false;

      allKeys.forEach((key) => {
        // Compare the previous and current values for each key
        if (previousProps[key] !== props[key]) {
          changes[key] = {
            from: previousProps[key],
            to: props[key],
          };
          hasChanges = true;
        }
      });

      // If there are any changes, log them to the console
      if (hasChanges) {
        console.log('[why-did-you-update]', name, changes);
      }
    }

    // Update the ref with the current props for the next render
    previousPropsRef.current = props;
  }); //     No dependency array means this effect runs after every render
}

export default useWhyDidYouUpdate;