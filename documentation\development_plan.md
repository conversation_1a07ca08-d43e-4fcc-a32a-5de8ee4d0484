# Final Development Plan: MISMO.DJ - Browser-Based DJ App (TypeScript, React, MobX-State-Tree, shadcn/ui, wavesurfer.js, Dexie.js, FSA) - REVISED STAGES V2

**Project Name:** MISMO.DJ

**Description:**
A **Chrome-first** browser-based DJ application built with React, MobX-State-Tree, and TypeScript. It features persistent library management via folder scanning (using File System Access API), background file detection & analysis, persistent track info/analysis storage (IndexedDB/Dexie), multi-deck playback with waveform visualization (wavesurfer.js), mixing controls, effects, and MIDI support. Styling is handled by Tailwind CSS, leveraging shadcn/ui components.

**Objective:** Generate the codebase for MISMO.DJ based on the following specifications and step-by-step plan, prioritizing library building, background processing, and analysis before playback features.

**Core Technologies:**

*   **Language:** TypeScript
*   **Frontend Framework:** React
*   **State Management:** MobX-State-Tree (MST)
*   **UI Reactivity:** `mobx-react-lite`
*   **UI Components:** shadcn/ui (built on Radix UI)
*   **Styling:** Tailwind CSS
*   **Waveform Visualization:** wavesurfer.js (Used from Stage 3 onwards)
*   **Database:** IndexedDB (via Dexie.js wrapper)
*   **File System Access:** File System Access API (FSA) - Target Chrome/Edge initially.
*   **Metadata Reading:** jsmediatags (or similar library)
*   **Audio:** Web Audio API (Used from Stage 3 onwards)
*   **Background Processing:** Web Workers (Primarily for Analysis in Stage 2)
*   **MIDI:** Web MIDI API (Later Stage)
*   **Build Tool:** Vite

**General Instructions for LLM:**

*   **Code Structure:** Strictly adhere to specified directory structure (`.ts`/`.tsx`).
*   **Modularity:** Create reusable React components, MST models, and services.
*   **TypeScript Usage:** Employ TypeScript diligently (MST models, props, functions, services). Use `types.frozen()` for non-serializable types like `AudioBuffer`, `FileSystemDirectoryHandle`.
*   **Immutability (MST):** All state modifications within MST `.actions()`. Use `flow` for async actions.
*   **Reactivity (MobX):** Wrap relevant React components with `observer`.
*   **Component Design:** Functional components with hooks. Keep UI focused, logic in stores/services.
*   **Styling (Tailwind/shadcn):** Use Tailwind utilities. Add/use shadcn/ui components.
*   **Database (Dexie):** Implement interactions via `DatabaseService.ts`. MST actions update state *then* call DB service asynchronously. Hydrate state from DB on load. **DB is critical from Stage 1.**
*   **File System Access (FSA):** Use `window.showDirectoryPicker()` in Stage 1. Store `FileSystemDirectoryHandle` objects (likely in IndexedDB via `DatabaseService`). Implement recursive directory traversal using async iterators (`handle.values()`). Handle permissions (`queryPermission`, `requestPermission`). Gracefully handle cases where FSA is unavailable (log error initially, potential fallback later). **Acknowledge Chrome/Edge limitation.**
*   **Background Scanning/Analysis:** Implement as loops/queues managed by MST stores/services, running while the app tab is active. Use `setInterval` or chained `setTimeout` for periodic checks. Provide UI controls for Start/Pause/Resume.
*   **Metadata Reading:** Integrate `jsmediatags` in Stage 1.
*   **WaveSurfer Integration:** Implement in Stage 3. Sync with MST `DeckModel`.
*   **Error Handling:** Implement `try...catch`, Error Boundaries, logging. Especially for FSA permissions and file access.
*   **Code Style:** Use Prettier/ESLint.
*   **Comments:** Add JSDoc/TSDoc comments.
*   **Testing Stubs:** Include `// Test: ...` comments.

**Phase 0: Project Setup**

1.  **Environment:** Node.js, npm/yarn.
2.  **Initialize Project:** `npm create vite@latest mismo-dj --template react-ts`, `cd`, `npm install`.
3.  **Install Core Dependencies:** `npm install mobx mobx-react-lite mobx-state-tree wavesurfer.js dexie jsmediatags`.
4.  **Install Dev Dependencies:** `npm install -D tailwindcss postcss autoprefixer @types/wavesurfer.js @types/dexie @types/jsmediatags @types/wicg-file-system-access @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint ... prettier ...`.
5.  **Setup Tailwind CSS:** Configure files, add directives, import CSS.
6.  **Setup shadcn/ui:** `npx shadcn-ui@latest init`.
7.  **State Management Setup (MST):** Create `src/stores/`, define initial models (`RootStore.ts`, `LibraryStore.ts`, `UiStore.ts`, `SettingsStore.ts`). Set up Context provider.
8.  **Database Setup (Dexie - `src/services/DatabaseService.ts`):**
    *   Define interfaces: `TrackInfo`, `TrackAnalysis`, `TrackCues`, `AppSetting`, `MidiMapping`, `StoredDirectoryHandle`.
    *   `TrackInfo`: Add `isHidden: boolean`, `analysisStatus: "Pending" | "Analyzing" | "Analyzed" | "Error"`.
    *   `StoredDirectoryHandle`: `{ id: string; name: string; handle: FileSystemDirectoryHandle }` (Handle stored directly - Dexie supports this).
    *   `AppDatabase extends Dexie`: Schema `version(1).stores`:
        *   `tracks: 'id, filePath, isHidden, analysisStatus, artist, genre, rating',`
        *   `analysis: 'trackId',`
        *   `cues: 'trackId',`
        *   `settings: 'key',`
        *   `midiMappings: 'id',`
        *   `trackedDirectories: 'id'` // Store directory handles
    *   Implement async CRUD methods for all tables, including `trackedDirectories`.
    *   Export singleton `db` instance.
9.  **Directory Structure:** Create standard directories.
10. **Version Control:** `git init`, `.gitignore`, initial commit, remote.
11. **Linting & Formatting:** Configure tools, add scripts.
12. **TypeScript Configuration:** Review/adjust `tsconfig.json`.
13. **Basic Verification:** `npm run dev`. Check base app renders.

**Phase 1: Stage 1 - File Management & Library Building (FSA)**

1.  **Add shadcn Components:** `button`, `card`, `input`, `table`, `scroll-area`, `progress`, `label`, `tooltip`, `alert`, `alert-dialog`.
2.  **State Management (`src/stores/LibraryStore.ts`):**
    *   Define `TrackInfoModel` (matches DB interface, including `isHidden`, `analysisStatus`). Add action `hide()`.
    *   Define `TrackedDirectoryModel = types.model({ id: types.identifier, name: types.string, handle: types.frozen<FileSystemDirectoryHandle>() })`.
    *   `LibraryStoreModel`:
        *   Properties: `tracks: types.map(TrackInfoModel)`, `trackedDirectories: types.map(TrackedDirectoryModel)`, `scanStatus: types.enumeration(["Idle", "Scanning", "Paused", "Error"])`, `scanProgress: 0`, `scanTotal: 0`, `scanCurrentFile: types.maybeNull(types.string)`.
        *   Actions: `addDirectory = flow(function*() { ... })`, `removeDirectory = flow(function*(dirId: string) { ... })`, `startScanning()`, `pauseScanning()`, `resumeScanning()`, `_scanLoop = flow(function*() { ... })`, `_addOrUpdateTrack(trackData)`, `_setScanProgress(...)`, `_finishScan()`, `hydrateFromDb = flow(function*() { ... })`.
    *   `addDirectory`: Uses `window.showDirectoryPicker()`, requests permission (`handle.requestPermission({ mode: 'read' })`), creates `TrackedDirectoryModel` instance, saves to DB (`db.saveTrackedDirectory`), adds to `self.trackedDirectories` map. Handle errors/user cancellation.
    *   `removeDirectory`: Removes from map and calls `db.deleteTrackedDirectory`.
    *   `startScanning`: Sets status="Scanning", calls `_scanLoop`. `pause/resumeScanning` update status.
    *   `_scanLoop`: (Use async generator or recursive flow). Iterates through `self.trackedDirectories`. For each handle, recursively iterates using `handle.values()`. For each `FileSystemFileHandle`:
        *   Construct unique `trackId` (e.g., combining directory path and file name).
        *   Check `db.getTrack(trackId)` to see if known and if `isHidden`. If hidden, skip.
        *   If new or needs update (check file `lastModified`? Defer this complexity initially):
            *   Get `File` object: `fileHandle.getFile()`.
            *   Update `scanCurrentFile` state for UI.
            *   Call `MetadataReaderService.readMetadata(file)`. Handle metadata read errors.
            *   Prepare `TrackInfo` object (path, name, metadata, `isHidden: false`, `analysisStatus: 'Pending'`).
            *   Call `self._addOrUpdateTrack(trackInfo)` (updates MST map and `db.addTrack`).
            *   Update progress.
            *   Yield periodically (`yield Promise.resolve()` or `setTimeout`) to prevent blocking.
            *   Break loop if `self.scanStatus === 'Paused'`.
    *   `hydrateFromDb`: Called on app load. Fetches directories from `db.trackedDirectories` and tracks from `db.tracks`, populates MST maps. **Important:** Verify permissions on directory handles using `handle.queryPermission()` and potentially `handle.requestPermission()` if needed. Handle cases where permission was revoked.
3.  **Services:**
    *   `DatabaseService.ts`: Implement `trackedDirectories` CRUD methods.
    *   `MetadataReaderService.ts`: Implement `readMetadata(file: File)` using `jsmediatags`.
4.  **UI Layer (`src/components/`):**
    *   `LibraryManagerComponent.tsx` (`observer`):
        *   "Add Folder" button -> `libraryStore.addDirectory`.
        *   `FolderListComponent.tsx`: Displays `libraryStore.trackedDirectories` with "Remove" buttons (`libraryStore.removeDirectory`).
        *   "Scan"/"Pause"/"Resume" buttons -> `libraryStore` scan actions.
        *   Display scan status, progress bar (shadcn `<Progress>`), current file being scanned.
        *   Display alerts for errors (e.g., permission denied).
    *   `TrackListComponent.tsx` (`observer`):
        *   Display tracks where `!track.isHidden`. Use shadcn `<Table>`, `<ScrollArea>`.
        *   Add "Hide" button/menu item -> `track.hide()`.

**Phase 2: Stage 2 - Track Data Discovery & Analysis**

1.  **Add shadcn Components:** `icon` (e.g., `Check`, `Loader`, `XCircle`), potentially `context-menu`.
2.  **Background Processing (`src/workers/analysis.worker.ts`):** Implement analysis logic.
3.  **State Management:**
    *   Create `AnalysisQueueStore.ts` (or add logic to `LibraryStore`):
        *   Properties: `analysisStatus: types.enumeration(["Idle", "Running", "Paused"])`, `queue: types.array(types.reference(TrackInfoModel))` (reference tracks needing analysis), `activeWorkers: types.number`.
        *   Actions: `enqueuePendingTracks()`, `startAnalysisProcess()`, `pauseAnalysisProcess()`, `resumeAnalysisProcess()`, `_processQueue = flow(function*() { ... })`.
    *   `enqueuePendingTracks`: Finds tracks in `libraryStore` with `analysisStatus === 'Pending'` and adds their references to the queue.
    *   `startAnalysisProcess`: Sets status="Running", calls `_processQueue`.
    *   `_processQueue`: While queue is not empty and status is "Running":
        *   Dequeue a track reference (`track`).
        *   Call `track.requestAnalysis()`. This action (on `TrackInfoModel`) now just handles the worker communication for *that specific track*.
        *   Potentially manage multiple concurrent workers (e.g., up to `navigator.hardwareConcurrency`).
        *   Yield periodically. Break if paused.
    *   `TrackInfoModel`: Actions `requestAnalysis` (manages single worker), `_setAnalysisResults` (updates self + DB), `_setAnalysisError` (updates self + DB).
4.  **UI Layer (`src/components/`):**
    *   Modify `LibraryManagerComponent.tsx`: Add "Analyze Pending"/"Pause Analysis"/"Resume Analysis" buttons connected to `AnalysisQueueStore` actions. Display overall analysis status.
    *   Modify `TrackListComponent.tsx` (`observer`):
        *   Display individual `track.analysisStatus` (Idle, Analyzing, Analyzed, Error) with icons.
        *   Add button/menu item "Re-Analyze" -> `track.requestAnalysis()`.
        *   Add UI (modal?) to view/edit analysis results, calling manual edit actions on `TrackInfoModel`.

**Phase 3: Stage 3 - Basic DJ Playing & Waveforms**

1.  **Add shadcn Components:** `button`, `slider`, `label`, `card`.
2.  **State Management (`DeckModel.ts`):**
    *   Properties: `id`, `currentTrackRef: types.maybeNull(types.safeReference(TrackInfoModel))`, playback state, volume, cues (`hotCues: types.array(types.maybeNull(types.number))`).
    *   `.volatile`: `audioEngine`, `wavesurfer`.
    *   `.afterCreate`: Instantiate `DeckAudio`.
    *   Action `loadTrack = flow(function*(trackInfo: Instance<typeof TrackInfoModel>) { ... })`:
        *   Sets `self.currentTrackRef = trackInfo`.
        *   **Get File:** Use `LibraryStore` to find the corresponding `TrackedDirectoryModel` handle based on `trackInfo.filePath`. Use `dirHandle.getFileHandle(relativePath)` then `fileHandle.getFile()`. Handle potential errors (file moved, permissions revoked).
        *   Call `FileLoaderService.loadAndDecodeAudio(file)`.
        *   Call `self.audioEngine.loadAudioBuffer`.
        *   Load cues from DB: `const cues = yield db.getCues(trackInfo.id)`. Update `self.hotCues`.
        *   Trigger wavesurfer load using the `File` object: `self.wavesurfer.load(file)`.
    *   Other actions (`play`, `pause`, `setVolume`, `setHotCue`, etc.) update state, call `audioEngine`, sync `wavesurfer`, and `setHotCue` calls `db.saveCues`.
3.  **Audio Engine (`DeckAudio.ts`):** Implement playback controls.
4.  **Services (`FileLoaderService.ts`):** Implement `loadAndDecodeAudio`.
5.  **UI Layer:**
    *   `DeckComponent.tsx` (`observer`): Use shadcn components. Display info from `deckModel.currentTrackRef`. Connect controls to actions.
    *   `WaveformComponent.tsx` (`observer`): Integrate `wavesurfer.js`. Sync with `deckModel`. Display cues/beatgrid (from `deckModel.currentTrackRef`) as overlays or via plugins.
    *   `TrackListComponent.tsx`: Add "Load to Deck A/B" button/drag -> `rootStore.decks[deckIndex].loadTrack(trackInfo)`.

**Phase 4 - 10:** (Follow previous plan's logic, adapting for new Stage 1-3 foundation)

*   **Multiple Decks:** Refactor state (`RootStore`), audio (`DeckManager`, `Mixer`), UI. Persist/hydrate `masterVolume`.
*   **Mixing:** Add MST state/actions, audio logic, UI controls (shadcn/custom).
*   **Effects:** Add audio nodes, MST state/actions, UI controls.
*   **Recording:** Implement `Recorder.ts`, MST state/actions, UI.
*   **History:** Implement `HistoryService`, `HistoryStore` (optional DB persistence), UI.
*   **MIDI:** Implement `MIDIService`, `MidiStore` (load/save mappings via DB), UI config.
*   **Stems:** Refactor `DeckAudio`/`DeckModel`, add `StemService`, update UI.

**Final LLM Instructions Additions:**

*   Implement **File System Access API** (`window.showDirectoryPicker`, `handle.values()`, permissions) in `LibraryStore` actions. Handle errors and the Chrome-only limitation.
*   Store and retrieve **`FileSystemDirectoryHandle`** objects using `DatabaseService`.
*   Implement the **background scanning loop** (`_scanLoop`) in `LibraryStore`, ensuring it's pausable and checks the `isHidden` flag in the DB.
*   Implement the **analysis queue and processing loop** (`AnalysisQueueStore` or `LibraryStore`), triggering individual track analysis via `TrackInfoModel.requestAnalysis`.
*   Add **`isHidden` and `analysisStatus`** properties and related actions/logic to `TrackInfoModel` and the DB schema.
*   Modify **`DeckModel.loadTrack`** to use stored directory handles to retrieve `File` objects via FSA for loading audio and wavesurfer.
*   Ensure **UI controls for managing folders, scanning, analysis (Start/Pause/Resume), and hiding tracks** are implemented using shadcn components.

This significantly revised plan provides a detailed roadmap for building the app with the requested workflow changes, emphasizing the FSA, background processing, and database integration from the start.