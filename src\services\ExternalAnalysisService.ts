import { RootStoreType } from '../stores/RootStore';
import { TrackAnalysis } from './DatabaseService';
import { makeAutoObservable } from 'mobx';

/**
 * Interface for the external analysis data structure from allin1 Python package
 */
interface ExternalAnalysisData {
  path: string;
  bpm: number;
  beats: number[];
  downbeats: number[];
  beat_positions: number[];
  segments: Array<{
    start: number;
    end: number;
    label: string;
  }>;
}

/**
 * Service for handling external analysis data from the allin1 Python package
 */
export class ExternalAnalysisService {
  rootStore: RootStoreType;
  isLoading: boolean = false;
  currentTrackId: string | null = null;
  loadErrors: Map<string, string> = new Map();

  constructor(rootStore: RootStoreType) {
    this.rootStore = rootStore;
    makeAutoObservable(this);
  }

  /**
   * Checks if external analysis data exists for a track
   * @param trackId The ID of the track to check
   * @returns A promise resolving to true if external analysis data exists, false otherwise
   */
  async checkExternalAnalysisExists(trackId: string): Promise<boolean> {
    try {
      // Get track info
      const track = await this.rootStore.databaseService.getTrack(trackId);
      if (!track) {
        console.error(`ExternalAnalysisService: Track info not found for track ${trackId}`);
        return false;
      }

      // Get directory info
      const dirInfo = await this.rootStore.databaseService.getTrackedDirectory(track.directoryId!);
      if (!dirInfo) {
        console.error(`ExternalAnalysisService: Directory info not found for track ${trackId}`);
        return false;
      }

      // Check permissions
      const permissionStatus = await dirInfo.handle.queryPermission({ mode: 'read' });
      if (permissionStatus !== 'granted') {
        const newPermission = await dirInfo.handle.requestPermission({ mode: 'read' });
        if (newPermission !== 'granted') {
          console.error(`ExternalAnalysisService: Permission denied for directory ${dirInfo.name}`);
          return false;
        }
      }

      // Get the parent directory of the track
      const filePath = track.filePath;
      const lastSlashIndex = filePath.lastIndexOf('/');
      const parentPath = lastSlashIndex !== -1 ? filePath.substring(0, lastSlashIndex) : '';

      // Try to access the 'struct' folder
      try {
        let parentDir;
        if (parentPath) {
          // Navigate to the parent directory
          const pathParts = parentPath.split('/');
          let currentDir = dirInfo.handle;

          for (const part of pathParts) {
            if (part) {
              currentDir = await currentDir.getDirectoryHandle(part);
            }
          }
          parentDir = currentDir;
        } else {
          // Track is directly in the root directory
          parentDir = dirInfo.handle;
        }

        // Try to access the 'struct' folder
        try {
          const structDir = await parentDir.getDirectoryHandle('struct');

          // Get the filename without extension
          const filename = track.filename;
          const fileNameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));

          // Try to access the JSON file
          try {
            await structDir.getFileHandle(`${fileNameWithoutExt}.json`);
            return true; // File exists
          } catch (e) {
            console.log(`ExternalAnalysisService: JSON file not found for track ${trackId}`);
            return false;
          }
        } catch (e) {
          console.log(`ExternalAnalysisService: 'struct' folder not found for track ${trackId}`);
          return false;
        }
      } catch (e) {
        console.error(`ExternalAnalysisService: Error accessing parent directory for track ${trackId}:`, e);
        return false;
      }
    } catch (e) {
      console.error(`ExternalAnalysisService: Error checking external analysis for track ${trackId}:`, e);
      return false;
    }
  }

  /**
   * Loads external analysis data for a track
   * @param trackId The ID of the track to load external analysis for
   * @returns A promise resolving to the loaded analysis data or null if not found
   */
  async loadExternalAnalysis(trackId: string): Promise<Partial<TrackAnalysis> | null> {
    this.isLoading = true;
    this.currentTrackId = trackId;
    this.loadErrors.delete(trackId);

    try {
      // Get track info
      const track = await this.rootStore.databaseService.getTrack(trackId);
      if (!track) {
        throw new Error(`Track info not found for track ${trackId}`);
      }

      // Get directory info
      const dirInfo = await this.rootStore.databaseService.getTrackedDirectory(track.directoryId!);
      if (!dirInfo) {
        throw new Error(`Directory info not found for track ${trackId}`);
      }

      // Get the parent directory of the track
      const filePath = track.filePath;
      const lastSlashIndex = filePath.lastIndexOf('/');
      const parentPath = lastSlashIndex !== -1 ? filePath.substring(0, lastSlashIndex) : '';
      const filename = track.filename;
      const fileNameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));

      // Navigate to the parent directory
      let parentDir;
      if (parentPath) {
        const pathParts = parentPath.split('/');
        let currentDir = dirInfo.handle;

        for (const part of pathParts) {
          if (part) {
            currentDir = await currentDir.getDirectoryHandle(part);
          }
        }
        parentDir = currentDir;
      } else {
        // Track is directly in the root directory
        parentDir = dirInfo.handle;
      }

      // Access the 'struct' folder
      const structDir = await parentDir.getDirectoryHandle('struct');

      // Access the JSON file
      const jsonFileHandle = await structDir.getFileHandle(`${fileNameWithoutExt}.json`);
      const jsonFile = await jsonFileHandle.getFile();
      const jsonText = await jsonFile.text();
      const externalData = JSON.parse(jsonText) as ExternalAnalysisData;

      // Convert external data to our format
      const analysisData: Partial<TrackAnalysis> = {
        trackId,
        externalAnalysis: {
          bpm: externalData.bpm,
          beats: externalData.beats,
          downbeats: externalData.downbeats,
          beat_positions: externalData.beat_positions,
          segments: externalData.segments
        },
        externalAnalysisDate: Date.now()
      };

      // Calculate average BPM from downbeats if available
      if (analysisData.externalAnalysis && externalData.downbeats && externalData.downbeats.length > 1) {
        let bpmValues: number[] = [];
        
        for (let i = 0; i < externalData.downbeats.length - 1; i++) {
          const currentDownbeat = externalData.downbeats[i];
          const nextDownbeat = externalData.downbeats[i + 1];
          const timeDiff = nextDownbeat - currentDownbeat;
          
          // Multiply by 4 since downbeats are every 4 beats in 4/4 time
          const calculatedBpm = timeDiff > 0 ? (60 / timeDiff) * 4 : 0;
          
          // Only include reasonable BPM values
          if (calculatedBpm > 60 && calculatedBpm < 200) {
            bpmValues.push(calculatedBpm);
          }
        }
        
        // Sort BPM values to handle outliers
        bpmValues.sort((a, b) => a - b);
        
        // Use a trimmed mean approach - remove the top and bottom 20%
        if (bpmValues.length > 5) {
          const trimAmount = Math.floor(bpmValues.length * 0.2);
          const trimmedValues = bpmValues.slice(trimAmount, bpmValues.length - trimAmount);
          bpmValues = trimmedValues;
        }
        
        // If we have valid BPM values, calculate and use the average
        if (bpmValues.length > 0) {
          const averageBpm = bpmValues.reduce((sum, bpm) => sum + bpm, 0) / bpmValues.length;
          console.log(`Average BPM: ${averageBpm}`);
          analysisData.externalAnalysis.bpm = averageBpm;
        }
      }

      // If our analysis doesn't have BPM but external does, use it
      if (!analysisData.bpm && externalData.bpm) {
        analysisData.bpm = externalData.bpm;
      }

      return analysisData;
    } catch (error: any) {
      console.error(`ExternalAnalysisService: Error loading external analysis for track ${trackId}:`, error);
      this.loadErrors.set(trackId, error.message || 'Unknown error');
      return null;
    } finally {
      this.isLoading = false;
      this.currentTrackId = null;
    }
  }

  /**
   * Imports external analysis data for a track and saves it to the database
   * @param trackId The ID of the track to import external analysis for
   * @returns A promise resolving to true if successful, false otherwise
   */
  async importExternalAnalysis(trackId: string): Promise<boolean> {
    try {
      // Load external analysis data
      const externalData = await this.loadExternalAnalysis(trackId);
      if (!externalData) {
        return false;
      }

      // Get existing analysis data
      const existingAnalysis = await this.rootStore.databaseService.getAnalysis(trackId);

      // Merge existing and external data
      const mergedData: TrackAnalysis = {
        trackId,
        bpm: existingAnalysis?.bpm || externalData.bpm,
        key: existingAnalysis?.key || externalData.key,
        firstBeatTime: existingAnalysis?.firstBeatTime,
        beatGridTimes: existingAnalysis?.beatGridTimes,
        waveformPeaks: existingAnalysis?.waveformPeaks,
        analysisDate: existingAnalysis?.analysisDate || Date.now(),
        externalAnalysis: externalData.externalAnalysis,
        externalAnalysisDate: externalData.externalAnalysisDate
      };

      // Save merged data
      await this.rootStore.databaseService.saveAnalysis(mergedData);

      // Update track status if needed
      if (!existingAnalysis) {
        await this.rootStore.databaseService.updateTrackAnalysisStatus(trackId, 'Analyzed');
      }

      return true;
    } catch (error) {
      console.error(`ExternalAnalysisService: Error importing external analysis for track ${trackId}:`, error);
      return false;
    }
  }
}


