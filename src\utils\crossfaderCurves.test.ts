import { linearCurve, constantPowerCurve, fastCutCurve } from './crossfaderCurves';

describe('Crossfader Curves', () => {
  // Helper for comparing floating point arrays
  const expectArraysCloseTo = (actual: [number, number], expected: [number, number], precision = 3) => {
    expect(actual[0]).toBeCloseTo(expected[0], precision);
    expect(actual[1]).toBeCloseTo(expected[1], precision);
  };

  describe('linearCurve', () => {
    it('should return [1, 0] for x = 0', () => {
      expectArraysCloseTo(linearCurve(0), [1, 0]);
    });

    it('should return [0, 1] for x = 1', () => {
      expectArraysCloseTo(linearCurve(1), [0, 1]);
    });

    it('should return [0.5, 0.5] for x = 0.5', () => {
      expectArraysCloseTo(linearCurve(0.5), [0.5, 0.5]);
    });

    it('should return [0.75, 0.25] for x = 0.25', () => {
      expectArraysCloseTo(linearCurve(0.25), [0.75, 0.25]);
    });

    it('should return [0.25, 0.75] for x = 0.75', () => {
      expectArraysCloseTo(linearCurve(0.75), [0.25, 0.75]);
    });

    it('should clamp x < 0 to 0, returning [1, 0]', () => {
      expectArraysCloseTo(linearCurve(-0.5), [1, 0]);
    });

    it('should clamp x > 1 to 1, returning [0, 1]', () => {
      expectArraysCloseTo(linearCurve(1.5), [0, 1]);
    });
  });

  describe('constantPowerCurve', () => {
    const sqrtHalf = Math.sqrt(0.5); // approx 0.70710678118

    it('should return [1, 0] for x = 0', () => {
      expectArraysCloseTo(constantPowerCurve(0), [1, 0]);
    });

    it('should return [0, 1] for x = 1', () => {
      expectArraysCloseTo(constantPowerCurve(1), [0, 1]);
    });

    it('should return [sqrt(0.5), sqrt(0.5)] for x = 0.5', () => {
      expectArraysCloseTo(constantPowerCurve(0.5), [sqrtHalf, sqrtHalf]);
    });

    it('should return correct values for x = 0.25', () => {
      const x = 0.25;
      const expectedLeft = Math.cos(x * Math.PI / 2);
      const expectedRight = Math.sin(x * Math.PI / 2);
      expectArraysCloseTo(constantPowerCurve(x), [expectedLeft, expectedRight]);
      expect(expectedLeft).toBeGreaterThan(0);
      expect(expectedLeft).toBeLessThan(1);
      expect(expectedRight).toBeGreaterThan(0);
      expect(expectedRight).toBeLessThan(1);
    });

    it('should return correct values for x = 0.75', () => {
      const x = 0.75;
      const expectedLeft = Math.cos(x * Math.PI / 2);
      const expectedRight = Math.sin(x * Math.PI / 2);
      expectArraysCloseTo(constantPowerCurve(x), [expectedLeft, expectedRight]);
      expect(expectedLeft).toBeGreaterThan(0);
      expect(expectedLeft).toBeLessThan(1);
      expect(expectedRight).toBeGreaterThan(0);
      expect(expectedRight).toBeLessThan(1);
    });

    it('should clamp x < 0 to 0, returning [1, 0]', () => {
      expectArraysCloseTo(constantPowerCurve(-0.5), [1, 0]);
    });

    it('should clamp x > 1 to 1, returning [0, 1]', () => {
      expectArraysCloseTo(constantPowerCurve(1.5), [0, 1]);
    });
  });

  describe('fastCutCurve', () => {
    it('should return [1, 0] for x = 0', () => {
      expectArraysCloseTo(fastCutCurve(0), [1, 0]);
    });

    it('should return [0, 1] for x = 1', () => {
      expectArraysCloseTo(fastCutCurve(1), [0, 1]);
    });

    it('should return [0.25, 0.25] for x = 0.5', () => {
      expectArraysCloseTo(fastCutCurve(0.5), [0.25, 0.25]);
    });

    it('should return correct values for x = 0.25', () => {
      // (1 - 0.25)^2 = 0.75^2 = 0.5625
      // 0.25^2 = 0.0625
      expectArraysCloseTo(fastCutCurve(0.25), [0.5625, 0.0625]);
    });

    it('should return correct values for x = 0.75', () => {
      // (1 - 0.75)^2 = 0.25^2 = 0.0625
      // 0.75^2 = 0.5625
      expectArraysCloseTo(fastCutCurve(0.75), [0.0625, 0.5625]);
    });

    it('should clamp x < 0 to 0, returning [1, 0]', () => {
      expectArraysCloseTo(fastCutCurve(-0.5), [1, 0]);
    });

    it('should clamp x > 1 to 1, returning [0, 1]', () => {
      expectArraysCloseTo(fastCutCurve(1.5), [0, 1]);
    });
  });
});
