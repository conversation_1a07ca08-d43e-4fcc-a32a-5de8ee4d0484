import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '@/components/ui/button';
import { useStore } from '@/contexts/StoreContext';
import { Instance } from 'mobx-state-tree';
import { TrackInfoModel } from '@/stores/LibraryStore';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog'; // Corrected path

interface LoadTrackButtonProps {
  track: Instance<typeof TrackInfoModel>;
  // deckIndex is removed as the button will now handle selection or direct load
}

const LoadTrackButton: React.FC<LoadTrackButtonProps> = observer(({ track }) => {
  const { decks } = useStore(); // gets decks from root store
  const [isDeckSelectorOpen, setIsDeckSelectorOpen] = useState(false);
  // selectedTrackIdForLoad is implicitly track.id from props, so not needed in state here

  const handleLoadTrackAction = () => {
    if (decks.length === 1) {
      decks[0].loadTrack(track.id);
    } else if (decks.length > 1) {
      setIsDeckSelectorOpen(true);
    } else {
      // No decks available, maybe log an error or disable the button
      console.warn("No decks available to load track.");
    }
  };

  const handleSelectDeckAndLoad = (deckId: string) => {
    const selectedDeck = decks.find(d => d.id === deckId);
    if (selectedDeck) {
      selectedDeck.loadTrack(track.id);
    }
    setIsDeckSelectorOpen(false);
  };

  return (
    <>
      <Button 
        variant="outline" 
        size="sm" 
        onClick={handleLoadTrackAction}
      >
        Load Track {/* Changed text slightly, can be "Load to Deck" too */}
      </Button>

      {decks.length > 1 && (
        <Dialog open={isDeckSelectorOpen} onOpenChange={setIsDeckSelectorOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Select Deck</DialogTitle>
              <DialogDescription>
                Choose which deck to load "{track.title || track.filename}".
              </DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-2">
              {decks.map((deck) => (
                <Button
                  key={deck.id}
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSelectDeckAndLoad(deck.id)}
                >
                  Load to Deck {decks.findIndex(d => d.id === deck.id) + 1} ({deck.id})
                </Button>
              ))}
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="ghost">Cancel</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
});

export default LoadTrackButton;
