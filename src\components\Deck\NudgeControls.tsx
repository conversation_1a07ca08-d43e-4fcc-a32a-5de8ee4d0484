import React, { useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, RotateCcw } from 'lucide-react';

interface NudgeControlsProps {
  deck: DeckStoreInstance;
}

const NudgeControls: React.FC<NudgeControlsProps> = observer(({ deck }) => {
  const nudgeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const nudgeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up intervals on unmount
  useEffect(() => {
    return () => {
      if (nudgeIntervalRef.current) {
        clearInterval(nudgeIntervalRef.current);
      }
      if (nudgeTimeoutRef.current) {
        clearTimeout(nudgeTimeoutRef.current);
      }
    };
  }, []);

  const startNudge = (direction: 'forward' | 'backward') => {
    if (!deck.loadedTrack) return;

    // Clear any existing nudge
    stopNudge();

    const nudgeStep = 0.1; // 10% nudge step
    const nudgeValue = direction === 'forward' ? nudgeStep : -nudgeStep;
    
    // Apply initial nudge
    deck.setNudgeValue(nudgeValue);

    // Set up continuous nudging while button is held
    nudgeIntervalRef.current = setInterval(() => {
      const currentNudge = deck.nudgeValue;
      const newNudge = Math.max(-1, Math.min(1, currentNudge + (nudgeValue * 0.5)));
      deck.setNudgeValue(newNudge);
    }, 100); // Update every 100ms

    // Auto-release after 5 seconds to prevent getting stuck
    nudgeTimeoutRef.current = setTimeout(() => {
      stopNudge();
    }, 5000);
  };

  const stopNudge = () => {
    // Clear intervals
    if (nudgeIntervalRef.current) {
      clearInterval(nudgeIntervalRef.current);
      nudgeIntervalRef.current = null;
    }
    if (nudgeTimeoutRef.current) {
      clearTimeout(nudgeTimeoutRef.current);
      nudgeTimeoutRef.current = null;
    }

    // Reset nudge value
    deck.resetNudge();
  };

  const handleMouseDown = (direction: 'forward' | 'backward') => {
    startNudge(direction);
  };

  const handleMouseUp = () => {
    stopNudge();
  };

  const handleTouchStart = (direction: 'forward' | 'backward') => {
    startNudge(direction);
  };

  const handleTouchEnd = () => {
    stopNudge();
  };

  // Prevent context menu on long press
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
  };

  const isNudging = deck.nudgeValue !== 0;
  const nudgePercentage = Math.round(deck.nudgeValue * 100);

  return (
    <div className="space-y-2">
      {/* Nudge Controls */}
      <div className="flex items-center gap-2">
        {/* Backward Nudge */}
        <Button
          variant={deck.nudgeValue < 0 ? "default" : "outline"}
          size="sm"
          disabled={!deck.loadedTrack}
          onMouseDown={() => handleMouseDown('backward')}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp} // Stop nudging if mouse leaves button
          onTouchStart={() => handleTouchStart('backward')}
          onTouchEnd={handleTouchEnd}
          onContextMenu={handleContextMenu}
          className="flex items-center gap-1 select-none"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="text-xs">-</span>
        </Button>

        {/* Nudge Status Display */}
        <div className="flex-1 text-center">
          <div className="text-xs font-medium">
            {isNudging ? (
              <span className={nudgePercentage > 0 ? "text-blue-500" : "text-orange-500"}>
                {nudgePercentage > 0 ? '+' : ''}{nudgePercentage}%
              </span>
            ) : (
              <span className="text-muted-foreground">NUDGE</span>
            )}
          </div>
          {isNudging && (
            <div className="w-full bg-muted rounded-full h-1 mt-1">
              <div
                className={`h-1 rounded-full transition-all duration-100 ${
                  nudgePercentage > 0 ? 'bg-blue-500' : 'bg-orange-500'
                }`}
                style={{
                  width: `${Math.abs(nudgePercentage)}%`,
                  marginLeft: nudgePercentage < 0 ? `${100 - Math.abs(nudgePercentage)}%` : '0',
                }}
              />
            </div>
          )}
        </div>

        {/* Forward Nudge */}
        <Button
          variant={deck.nudgeValue > 0 ? "default" : "outline"}
          size="sm"
          disabled={!deck.loadedTrack}
          onMouseDown={() => handleMouseDown('forward')}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp} // Stop nudging if mouse leaves button
          onTouchStart={() => handleTouchStart('forward')}
          onTouchEnd={handleTouchEnd}
          onContextMenu={handleContextMenu}
          className="flex items-center gap-1 select-none"
        >
          <span className="text-xs">+</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Reset Button */}
      {isNudging && (
        <div className="flex justify-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={stopNudge}
            className="flex items-center gap-1 text-xs"
          >
            <RotateCcw className="h-3 w-3" />
            Reset
          </Button>
        </div>
      )}

      {/* Nudge Info */}
      <div className="text-xs text-muted-foreground text-center">
        Hold to nudge tempo • Works with sync enabled
      </div>
    </div>
  );
});

export default NudgeControls;
