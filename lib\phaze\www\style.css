*, *:before, *:after {
    box-sizing: border-box;
}

html, body {
    height: 100%;
}

body {
    padding: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    display: flex;
    justify-content: center;
    background-color: #eee;
}

.wrapper {
    height: 100%;
    padding: 10% 0 0 0;
}

.controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.controls #play-pause {
    margin-right: auto;
}

.controls label {
  margin-left: 10px;
}

.controls label span {
  display: block;
  text-align: center;
}

#no-worklet {
    text-align: center;
    font-size: 1.1em;
    font-weight: bold;
}
