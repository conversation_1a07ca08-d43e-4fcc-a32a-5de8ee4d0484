import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useStore } from '@/contexts/StoreContext';
import { toast } from 'sonner';

/**
 * Button component to trigger checking for external analysis data
 */
export function ExternalAnalysisButton() {
  const { libraryStore } = useStore();
  const [isChecking, setIsChecking] = useState(false);

  const handleCheckExternalAnalysis = async () => {
    if (isChecking) return;
    
    try {
      setIsChecking(true);
      toast.info('Starting external analysis check...');
      await libraryStore.checkExternalAnalysisForAllTracks();
    } catch (error) {
      console.error('Error checking external analysis:', error);
      toast.error('Error checking external analysis');
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={handleCheckExternalAnalysis}
      disabled={isChecking}
    >
      {isChecking ? 'Checking...' : 'Check External Analysis'}
    </Button>
  );
}
