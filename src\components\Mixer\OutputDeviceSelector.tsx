// src/components/Mixer/OutputDeviceSelector.tsx

import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Card } from '../ui/card';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';

interface AudioDevice {
  deviceId: string;
  label: string;
  kind: string;
}

export const OutputDeviceSelector: React.FC = observer(() => {
  const rootStore = useStore();
  const { settingsStore } = rootStore;
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Enumerate audio output devices
  const enumerateDevices = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Request permission to access media devices
      await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Get all media devices
      const devices = await navigator.mediaDevices.enumerateDevices();
      
      // Filter for audio output devices
      const audioOutputDevices = devices
        .filter(device => device.kind === 'audiooutput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Audio Output ${device.deviceId.slice(0, 8)}`,
          kind: device.kind
        }));

      // Add default device option
      const devicesWithDefault = [
        { deviceId: 'default', label: 'Default Audio Output', kind: 'audiooutput' },
        ...audioOutputDevices
      ];

      setAudioDevices(devicesWithDefault);
    } catch (err) {
      console.error('Failed to enumerate audio devices:', err);
      setError('Failed to access audio devices. Please check permissions.');
      
      // Fallback to default device only
      setAudioDevices([
        { deviceId: 'default', label: 'Default Audio Output', kind: 'audiooutput' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load devices on component mount
  useEffect(() => {
    enumerateDevices();
    
    // Listen for device changes
    const handleDeviceChange = () => {
      enumerateDevices();
    };
    
    navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
    
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
    };
  }, []);

  const handleMasterOutputDeviceChange = async (deviceId: string) => {
    try {
      await settingsStore.setMasterOutputDeviceId(deviceId);
      await rootStore.audioRoutingManager.setOutputDevice(deviceId, 'master');
    } catch (error) {
      console.error('Failed to set master output device:', error);
      setError('Failed to set master output device');
    }
  };

  const handleHeadphoneOutputDeviceChange = async (deviceId: string) => {
    try {
      await settingsStore.setHeadphoneOutputDeviceId(deviceId);
      await rootStore.audioRoutingManager.setOutputDevice(deviceId, 'headphone');
    } catch (error) {
      console.error('Failed to set headphone output device:', error);
      setError('Failed to set headphone output device');
    }
  };

  return (
    <Card className={cn("p-4 rounded-lg shadow-md bg-card text-card-foreground flex flex-col space-y-4 w-full max-w-md mx-auto")}>
      <div className="w-full text-center">
        <h3 className="text-lg font-semibold">Audio Output Devices</h3>
      </div>

      {error && (
        <div className="text-sm text-red-500 bg-red-50 p-2 rounded">
          {error}
        </div>
      )}

      {/* Master Output Device */}
      <div className="space-y-2">
        <Label htmlFor="master-output-device" className="text-sm font-medium">
          Master Output
        </Label>
        <Select
          value={settingsStore.masterOutputDeviceId}
          onValueChange={handleMasterOutputDeviceChange}
          disabled={isLoading}
        >
          <SelectTrigger id="master-output-device">
            <SelectValue placeholder="Select master output device" />
          </SelectTrigger>
          <SelectContent>
            {audioDevices.map((device, index) => (
              <SelectItem key={index} value={device.deviceId}>
                {device.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Headphone Output Device */}
      <div className="space-y-2">
        <Label htmlFor="headphone-output-device" className="text-sm font-medium">
          Headphone Output
        </Label>
        <Select
          value={settingsStore.headphoneOutputDeviceId}
          onValueChange={handleHeadphoneOutputDeviceChange}
          disabled={isLoading}
        >
          <SelectTrigger id="headphone-output-device">
            <SelectValue placeholder="Select headphone output device" />
          </SelectTrigger>
          <SelectContent>
            {audioDevices.map((device, index) => (
              <SelectItem key={index} value={device.deviceId}>
                {device.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Refresh Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={enumerateDevices}
        disabled={isLoading}
        className="w-full"
      >
        {isLoading ? 'Refreshing...' : 'Refresh Devices'}
      </Button>

      {/* Device Count Info */}
      <div className="text-xs text-muted-foreground text-center pt-2 border-t">
        {audioDevices.length} audio output device{audioDevices.length !== 1 ? 's' : ''} available
      </div>
    </Card>
  );
});

export default OutputDeviceSelector;
