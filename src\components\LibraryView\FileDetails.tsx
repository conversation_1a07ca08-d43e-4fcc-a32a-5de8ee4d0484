import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { LibraryStoreInstance } from '@/stores/LibraryStore';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { RefreshCw, Database } from "lucide-react"; // Import icons
import { TrackAnalysis } from '@/services/DatabaseService';
import { useStore } from '@/contexts/StoreContext';

interface FileDetailsProps {
  selectedFileId: string | null;
  libraryStore: LibraryStoreInstance;
}

// Helper to format duration (seconds to mm:ss) - duplicated from FileList, consider moving to utils
const formatDuration = (seconds: number | undefined | null): string => {
  if (seconds === undefined || seconds === null || isNaN(seconds)) {
    return '--:--';
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Helper to format date
const formatDate = (timestamp: number | undefined | null): string => {
    if (!timestamp) return '-';
    try {
        return new Date(timestamp).toLocaleDateString();
    } catch (e) {
        return 'Invalid Date';
    }
};

const DetailItem: React.FC<{ label: string; value: React.ReactNode }> = ({ label, value }) => (
  <div className="mb-2">
    <p className="text-sm font-medium text-muted-foreground">{label}</p>
    <p className="text-sm break-words">{value || '-'}</p>
  </div>
);

const FileDetails: React.FC<FileDetailsProps> = observer(({ selectedFileId, libraryStore }) => {
  const rootStore = useStore();
  const selectedTrack = selectedFileId ? libraryStore.tracks.get(selectedFileId) : null;
  const [isCheckingExternal, setIsCheckingExternal] = useState(false);

  // Load analysis data when selected track changes
  useEffect(() => {
    if (selectedTrack && selectedTrack.analysisStatus === "Analyzed") {
      selectedTrack.loadAnalysisData();
    }
  }, [selectedTrack]);

  const handleReanalyze = () => {
    if (selectedTrack) {
      libraryStore.requestTrackAnalysis(selectedTrack.id);
    }
  };

  const handleCheckExternalAnalysis = async () => {
    if (!selectedTrack || isCheckingExternal) return;

    try {
      setIsCheckingExternal(true);
      const result = await libraryStore.checkExternalAnalysisForTrack(selectedTrack.id);
      if (result) {
        // If external analysis was found and imported, reload the track data
        await selectedTrack.loadAnalysisData();
      }
    } catch (error) {
      console.error('Error checking external analysis:', error);
    } finally {
      setIsCheckingExternal(false);
    }
  };

  if (!selectedTrack) {
    return (
      <div className="p-4 h-full flex items-center justify-center text-muted-foreground">
        Select a file to see details.
      </div>
    );
  }

  // Extract the relative path for display
  const displayPath = selectedTrack.filePath;

  return (
    <ScrollArea className="h-full p-4">
      <h3 className="text-lg font-semibold mb-4 break-words">{selectedTrack.title ?? selectedTrack.filename}</h3>

      {/* Analysis buttons */}
      <div className="mb-4 flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleReanalyze}
          disabled={selectedTrack.analysisStatus === "Analyzing"}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Re-analyze Track
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleCheckExternalAnalysis}
          disabled={isCheckingExternal}
          className="flex items-center gap-2"
        >
          <Database className="h-4 w-4" />
          {isCheckingExternal ? 'Checking...' : 'Check External Analysis'}
        </Button>
      </div>

      <DetailItem label="Artist" value={selectedTrack.artist} />
      <DetailItem label="Album" value={selectedTrack.album} />
      <DetailItem label="Genre" value={selectedTrack.genre} />
      <DetailItem label="Duration" value={formatDuration(selectedTrack.duration)} />
      <DetailItem label="Rating" value={selectedTrack.rating ? `${selectedTrack.rating}/5` : '-'} />
      <DetailItem label="File Name" value={selectedTrack.filename} />
      <DetailItem label="Path" value={displayPath} />
      <DetailItem label="Date Added" value={formatDate(selectedTrack.addedDate)} />
      <DetailItem label="Last Played" value={formatDate(selectedTrack.lastPlayedDate)} />
      <DetailItem label="Analysis Status" value={selectedTrack.analysisStatus} />
      {/* Add more details as needed */}

      {/* Add Analysis Data Section */}
      {selectedTrack.analysisStatus === "Analyzed" && selectedTrack.analysisData && (
        <>
          <h4 className="text-md font-semibold mt-6 mb-2">Analysis Data</h4>
          <DetailItem label="BPM" value={selectedTrack.analysisData.bpm?.toFixed(1) || '-'} />
          <DetailItem label="Key" value={selectedTrack.formattedKey} />
          <DetailItem 
            label="First Beat Time" 
            value={selectedTrack.analysisData.fvbirstBeatTime ? `${selectedTrack.analysisData.firstBeatTime.toFixed(3)}s` : '-'} 
          />
          <DetailItem label="Analysis Date" value={formatDate(selectedTrack.analysisData.analysisDate)} />

          {/* External Analysis Data */}
          {selectedTrack.analysisData.externalAnalysis && (
            <>
              <h4 className="text-md font-semibold mt-4 mb-2">External Analysis Data</h4>
              <DetailItem
                label="BPM (External)"
                value={selectedTrack.analysisData.externalAnalysis.bpm?.toFixed(1) || '-'}
              />
              <DetailItem
                label="First Beat Time (External)"
                value={selectedTrack.analysisData.externalAnalysis.beats?.length > 0 
                  ? `${selectedTrack.analysisData.externalAnalysis.beats[0].toFixed(3)}s` 
                  : '-'}
              />
              <DetailItem
                label="Beats Count"
                value={selectedTrack.analysisData.externalAnalysis.beats?.length.toString() || '-'}
              />
              <DetailItem
                label="Downbeats Count"
                value={selectedTrack.analysisData.externalAnalysis.downbeats?.length.toString() || '-'}
              />
              <DetailItem
                label="Segments Count"
                value={selectedTrack.analysisData.externalAnalysis.segments?.length.toString() || '-'}
              />

              {/* Display segments if available */}
              {selectedTrack.analysisData.externalAnalysis.segments &&
               selectedTrack.analysisData.externalAnalysis.segments.length > 0 && (
                <>
                  <h5 className="text-sm font-semibold mt-2 mb-1">Segments</h5>
                  <div className="text-xs max-h-32 overflow-y-auto border rounded p-1">
                    {selectedTrack.analysisData.externalAnalysis.segments.map((segment: {start: number, end: number, label: string}, index: number) => (
                      <div key={index} className="mb-1 flex justify-between">
                        <span>{segment.label}</span>
                        <span>{formatDuration(segment.start)} - {formatDuration(segment.end)}</span>
                      </div>
                    ))}
                  </div>
                </>
              )}

              <DetailItem
                label="External Analysis Date"
                value={formatDate(selectedTrack.analysisData.externalAnalysisDate)}
              />

              {/* Downbeat BPM Calculations */}
              {selectedTrack.analysisData.externalAnalysis?.downbeats && 
               selectedTrack.analysisData.externalAnalysis.downbeats.length > 1 && (
                <>
                  <h5 className="text-sm font-semibold mt-2 mb-1">Downbeat BPM Calculations</h5>
                  <div className="text-xs max-h-32 overflow-y-auto border rounded p-1">
                    {(() => {
                      // Calculate all BPMs and their average
                      const bpmValues: number[] = [];
                      
                      const downbeats = selectedTrack.analysisData.externalAnalysis.downbeats;
                      return (
                        <>
                          {downbeats.slice(0, -1).map((downbeat, index) => {
                            if (index < downbeats.length - 1) {
                              const nextDownbeat = downbeats[index + 1];
                              const timeDiff = nextDownbeat - downbeat;
                              // Multiply by 4 since downbeats are every 4 beats in 4/4 time
                              const calculatedBpm = timeDiff > 0 ? (60 / timeDiff) * 4 : 0;
                              
                              // Add to array for average calculation if it's a reasonable value
                              if (calculatedBpm > 60 && calculatedBpm < 200) {
                                bpmValues.push(calculatedBpm);
                              }
                              
                              return (
                                <div key={index} className="mb-1 flex justify-between">
                                  <span>Downbeats {index+1}-{index+2}</span>
                                  <span>{calculatedBpm.toFixed(1)} BPM ({timeDiff.toFixed(3)}s)</span>
                                </div>
                              );
                            }
                            return null;
                          })}
                          
                          {/* Show average BPM */}
                          {bpmValues.length > 0 && (
                            <div className="mt-2 pt-2 border-t flex justify-between font-semibold">
                              <span>Average BPM</span>
                              <span>
                                {(bpmValues.reduce((sum, bpm) => sum + bpm, 0) / bpmValues.length).toFixed(1)} BPM
                              </span>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                </>
              )}
            </>
          )}
        </>
      )}
    </ScrollArea>
  );
});

export default FileDetails;










