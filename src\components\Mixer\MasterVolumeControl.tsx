// src/components/Mixer/MasterVolumeControl.tsx

import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Card } from '../ui/card';
import { Label } from '../ui/label';
import { Fader } from '../ui/fader';
import { cn } from '../../lib/utils';
import AudioLevelMeters from '../ui/AudioLevelMeters';

export const MasterVolumeControl: React.FC = observer(() => {
  const rootStore = useStore();
  const { settingsStore } = rootStore;

  const handleMasterVolumeChange = (value: number) => {
    settingsStore.setMasterVolume(value);

    // Update the audio routing manager
    try {
      const masterPath = rootStore.audioRoutingManager.getMasterPath();
      masterPath.setMasterVolume(value);
    } catch (error) {
      console.error('Failed to update master volume:', error);
    }
  };

  return (
    <Card className={cn("p-4 rounded-lg shadow-md bg-card text-card-foreground flex flex-col space-y-4 w-full max-w-md mx-auto")}>
      <div className="w-full text-center">
        <h3 className="text-lg font-semibold">Master Volume</h3>
      </div>

      {/* Master Volume Fader */}
      <div className="space-y-2">
        <Label htmlFor="master-volume" className="text-sm font-medium">
          Master Volume
        </Label>
        <div className="px-2">
          <Fader
            id="master-volume"
            orientation="horizontal"
            value={settingsStore.masterVolume}
            onChange={handleMasterVolumeChange}
            defaultValue={1}
            min={0}
            max={1}
            step={0.01}
            className="w-full"
            enableDoubleClick={true}
          />
        </div>
        <div className="text-xs text-muted-foreground text-center">
          {Math.round(settingsStore.masterVolume * 100)}%
        </div>
      </div>

      {/* Audio Level Meters */}
      <AudioLevelMeters analyzer={(() => { try { return rootStore.audioRoutingManager.getMasterPath().getMasterAnalyzer() || null; } catch { return null; } })()} />
    </Card>
  );
});

export default MasterVolumeControl;
