import React, { useMemo, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import TreeView, { TreeViewItem, TreeViewIconMap } from '@/components/ui/tree-view';
// Import TrackedDirectoryModel as well
import { LibraryStoreInstance, TrackInfoModel, TrackedDirectoryModel } from '@/stores/LibraryStore';
import { Folder, Music } from 'lucide-react';
import { Instance } from 'mobx-state-tree'; // Import Instance type

interface DirectoryTreeViewProps {
  libraryStore: LibraryStoreInstance;
  onSelectDirectory: (path: string | null) => void;
}

// Helper function to build the nested structure
const buildNestedStructure = (tracks: LibraryStoreInstance['tracksList'], dirId: string): TreeViewItem[] => {
  const root: { [key: string]: TreeViewItem } = {};
  const dirPrefix = `${dirId}:`;

  // Add type for 'track'
  tracks.forEach((track: Instance<typeof TrackInfoModel>) => {
    if (!track.id.startsWith(dirPrefix)) return;

    const relativePath = track.filePath; // filePath is already relative in the store model
    const pathSegments = relativePath.split('/').filter(segment => segment !== '');

    // We only care about the directory path, not the filename
    const dirPathSegments = pathSegments.slice(0, -1);
    if (dirPathSegments.length === 0) return; // Skip files directly in the root

    let currentLevel = root;
    let currentPathId = `dir-${dirId}`; // Start with the tracked dir ID

    // Add types for 'segment' and 'index'
    dirPathSegments.forEach((segment: string, index: number) => {
      currentPathId += `/${segment}`;
      if (!currentLevel[segment]) {
        currentLevel[segment] = {
          id: currentPathId,
          name: segment,
          // type: 'folder',
          children: [],
        };
      }
      // Ensure children array exists before accessing it
      if (!currentLevel[segment].children) {
          currentLevel[segment].children = [];
      }
      // Navigate deeper, ensuring the next level is treated as a potential parent
      // This requires treating the children array as the next level's root
      // A bit complex, let's rethink the structure building slightly.

      // Alternative: Build a flat map first, then structure?
      // Or, ensure we are always assigning to the children of the *correct* parent object.
      // Let's stick to direct building for now.

      // Find the object representing the current segment in the *parent's* children array
      const parentChildren = index === 0 ? Object.values(root) : currentLevel[dirPathSegments[index-1]]?.children;
      if (!parentChildren) return; // Should not happen if logic is correct

      const segmentNode = parentChildren.find(child => child.name === segment);
      if (!segmentNode) return; // Should not happen

      // The *next* level's parent is this segmentNode
      currentLevel = segmentNode.children ? segmentNode.children.reduce((acc, child) => { acc[child.name] = child; return acc; }, {} as { [key: string]: TreeViewItem }) : {};
       // If children is undefined, initialize it for the next iteration
       if (segmentNode.children === undefined) {
           segmentNode.children = [];
       }
       // The next iteration will add children to segmentNode.children
       // We need to update currentLevel to point to the *object* representation of segmentNode.children
       // This seems overly complex. Let's simplify.

    });
  });

  // Let's rebuild the structure logic more clearly
  const folders = new Map<string, TreeViewItem>(); // Map: fullPathId -> TreeViewItem

  // Add type for 'track'
  tracks.forEach((track: Instance<typeof TrackInfoModel>) => {
      if (!track.id.startsWith(dirPrefix)) return;

      const relativePath = track.filePath;
      const pathSegments = relativePath.split('/').filter(segment => segment !== '');
      const dirPathSegments = pathSegments.slice(0, -1);

      let currentPathId = `dir-${dirId}`;
      let parentNode: TreeViewItem | undefined = undefined; // Keep track of the parent

      // Add types for 'segment' and 'index'
      dirPathSegments.forEach((segment: string, index: number) => {
          const previousPathId = currentPathId;
          currentPathId += `/${segment}`;

          if (!folders.has(currentPathId)) {
              const newNode: TreeViewItem = {
                  id: currentPathId,
                  name: segment,
                  // type: 'folder',
                  children: [],
              };
              folders.set(currentPathId, newNode);

              // Add to parent's children if parent exists
              parentNode = folders.get(previousPathId); // Get the parent node
              if (parentNode && parentNode.children) {
                  // Check if child already exists (e.g., added by another track)
                  if (!parentNode.children.some(child => child.id === newNode.id)) {
                      parentNode.children.push(newNode);
                  }
              }
          }
      });
  });

  // Filter for top-level folders within this tracked directory
  const topLevelFolders = Array.from(folders.values()).filter(folder => {
      // A folder is top-level if its ID contains only one '/' after the dirId part
      const pathPart = folder.id.substring(`dir-${dirId}/`.length);
      return !pathPart.includes('/');
  });


  return topLevelFolders;
};


const DirectoryTreeView: React.FC<DirectoryTreeViewProps> = observer(({ libraryStore, onSelectDirectory }) => {

  const treeData = useMemo((): TreeViewItem[] => {
    const root: TreeViewItem = {
      id: "local-music",
      name: "Local Music",
      // type: "root",
      children: [],
    };

    const trackedDirs = libraryStore.trackedDirectoriesList;
    const tracks = libraryStore.tracksList; // Get all non-hidden tracks

    // Add type for 'dir'
    root.children = trackedDirs.map((dir: Instance<typeof TrackedDirectoryModel>) => {
      const trackedDirNode: TreeViewItem = {
        id: `dir-${dir.id}`,
        name: dir.name,
        // type: 'tracked-dir',
        children: buildNestedStructure(tracks, dir.id), // Build nested structure here
      };
      return trackedDirNode;
    });

    return [root];
  }, [libraryStore.trackedDirectoriesList, libraryStore.tracksList]); // Depend on both lists

  const iconMap: TreeViewIconMap = useMemo(() => ({
    root: <Music className="h-4 w-4 text-purple-500" />,
    'tracked-dir': <Folder className="h-4 w-4 text-blue-500" />,
    folder: <Folder className="h-4 w-4 text-gray-500" />, // Generic folder icon
    // Add other types if needed
  }), []);

  const handleSelection = useCallback((selectedItems: TreeViewItem[]) => {
    if (selectedItems.length === 0) {
      onSelectDirectory(null); // No selection
      return;
    }

    const selectedItem = selectedItems[0]; // Assuming single selection for now

    // Construct the display path based on the ID structure
    let displayPath: string | null = null;
    if (selectedItem.id === 'local-music') {
      displayPath = "Local Music";
    } else if (selectedItem.id.startsWith('dir-')) {
      const parts = selectedItem.id.split('/');
      const dirId = parts[0].substring('dir-'.length);
      const trackedDir = libraryStore.trackedDirectories.get(dirId);
      if (trackedDir) {
          displayPath = `Local Music/${trackedDir.name}`;
          if (parts.length > 1) {
              displayPath += `/${parts.slice(1).join('/')}`;
          }
      }
    }

    onSelectDirectory(displayPath);

  }, [onSelectDirectory, libraryStore.trackedDirectories]);


  return (
    <TreeView
      data={treeData}
      iconMap={iconMap}
      onSelectionChange={handleSelection}
      className="p-2 h-full"
    />
  );
});

export default DirectoryTreeView;
