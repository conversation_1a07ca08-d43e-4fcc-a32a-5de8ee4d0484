// src/services/AudioRoutingDiagnostics.ts
import { AudioRoutingManager } from './AudioRoutingManager';

/**
 * Diagnose the master path connection
 */
export function diagnoseMasterPath(routingManager: AudioRoutingManager): void {
  const audioContext = (routingManager as any).audioContext;
  const masterPath = routingManager.getMasterPath();
  console.log('AudioRoutingManager: Diagnosing master path...');

  // Check if master path is connected internally
  console.log(`Master path nodes: ${masterPath.getNodeCount()}`);
  console.log(`Master path connected: ${masterPath.isPathConnected()}`);

  // Test direct connection to audio destination
  console.log('AudioRoutingManager: Testing direct connection to audio destination...');
  const testOsc = audioContext.createOscillator();
  const testGain = audioContext.createGain();
  testOsc.frequency.value = 880; // A5 note
  testOsc.type = 'sine';
  testGain.gain.value = 0.05; // Very low volume

  testOsc.connect(testGain);
  testGain.connect(audioContext.destination);

  testOsc.start();
  testOsc.stop(audioContext.currentTime + 0.5);

  console.log('AudioRoutingManager: Direct test tone should play for 0.5 seconds');
}

/**
 * Test the audio routing by generating a test tone through the master path
 */
export async function testAudioRouting(routingManager: AudioRoutingManager): Promise<void> {
  const audioContext = (routingManager as any).audioContext;
  const masterPath = routingManager.getMasterPath();
  console.log('AudioRoutingManager: Testing audio routing...');

  // Ensure initialization
  if (!(routingManager as any).isInitialized) {
    console.log('AudioRoutingManager: Not initialized, initializing now...');
    await routingManager.initialize();
  }

  // Ensure audio context is running
  if (audioContext.state === 'suspended') {
    console.log('AudioRoutingManager: Audio context suspended, resuming...');
    await audioContext.resume();
  }
  console.log(`AudioRoutingManager: Audio context state: ${audioContext.state}`);

  // Ensure master output is properly connected
  (routingManager as any).ensureMasterPathOutput();

  // Diagnose the master path
  console.log('AudioRoutingManager: Checking master path configuration...');
  console.log('Master path node count:', masterPath.getNodeCount());
  console.log('Master path connected:', masterPath.isPathConnected());
  console.log('Master volume:', masterPath.getMasterVolume());

  // Get and log all nodes in the master path
  const inputMixer = masterPath.getInputNode();
  const outputNode = masterPath.getOutputNode();
  console.log('Master path input node exists:', !!inputMixer);
  console.log('Master path output node exists:', !!outputNode);
  console.log('Master path output node destination connected:', !!outputNode.getWebAudioNode());

  // Ensure master path is connected
  masterPath.connect();
  console.log('AudioRoutingManager: Master path connection ensured');

  // Create a test oscillator
  const oscillator = audioContext.createOscillator();
  const gainNode = audioContext.createGain();

  oscillator.frequency.value = 440; // A4 note
  oscillator.type = 'sine';
  gainNode.gain.value = 0.1; // Low volume

  console.log('AudioRoutingManager: Created test oscillator and gain node');

  try {
    // Connect oscillator through gain node
    oscillator.connect(gainNode);

    // Get the master input node and verify it
    const masterInputNode = masterPath.getInputNode().getWebAudioNode();
    console.log('Master input node exists:', !!masterInputNode);

    // Connect to master input
    gainNode.connect(masterInputNode);
    console.log('AudioRoutingManager: Connected test tone to master path input');

    // Verify the final output connection
    const outputNodeGain = masterPath.getOutputNode().getGainNode();
    console.log('Output node gain value:', outputNodeGain.gain.value);
    console.log('Output node connections:', masterPath.isPathConnected());
    
    // Log the full connection chain
    console.log('Connection chain:', 
      'oscillator → gainNode → masterInput →',
      'masterVolume →',
      'masterAnalyzer →',
      'outputNode →',
      'destination'
    );

  } catch (error) {
    console.error('AudioRoutingManager: Failed to connect to master input:', error);
    throw error;
  }

  console.log(`AudioRoutingManager: Master volume: ${masterPath.getMasterVolume()}`);

  // Play test tone
  oscillator.start();
  oscillator.stop(audioContext.currentTime + 1);
  console.log('AudioRoutingManager: Test tone started, should play for 1 second');

  // Monitor levels through analyzer
  const analyzer = masterPath.getMasterAnalyzer();
  analyzer.startAnalysis();
  
  // Log analyzer levels for debugging
  const checkLevels = setInterval(() => {
    console.log('Master analyzer RMS:', analyzer.getRMS());
    console.log('Master analyzer Peak:', analyzer.getPeak());
  }, 100);

  // Stop checking levels after 1 second
  setTimeout(() => {
    clearInterval(checkLevels);
    analyzer.stopAnalysis();
  }, 1000);
}
