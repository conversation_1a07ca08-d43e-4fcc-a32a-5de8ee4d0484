"use client";

import { useState, useRef, useMemo, JSX } from "react";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from "@/components/ui/collapsible";
import {
  Folder,
  ChevronRight,
  ChevronDown,
  Box,
  Search,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

export interface TreeViewItem {
  id: string;
  name: string;
  children?: TreeViewItem[];
  icon?: React.ReactNode;
  data?: any;
}

export interface TreeViewIconMap {
  file?: React.ReactNode;
  folder?: React.ReactNode;
  [key: string]: React.ReactNode | undefined;
}

export interface TreeViewMenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  action: (items: TreeViewItem[]) => void;
}

export interface TreeViewProps {
  className?: string;
  data: TreeViewItem[];
  title?: string;
  showExpandAll?: boolean;
  searchPlaceholder?: string;
  getIcon?: (item: TreeViewItem, depth: number) => React.ReactNode;
  onSelectionChange?: (selectedItems: TreeViewItem[]) => void;
  onAction?: (action: string, items: TreeViewItem[]) => void;
  iconMap?: TreeViewIconMap;
  menuItems?: TreeViewMenuItem[];
}

interface TreeItemProps {
  item: TreeViewItem;
  depth?: number;
  selectedId: string | null;
  onSelect: (id: string) => void;
  expandedIds: Set<string>;
  onToggleExpand: (id: string, isOpen: boolean) => void;
  getIcon?: (item: TreeViewItem, depth: number) => React.ReactNode;
  onAction?: (action: string, items: TreeViewItem[]) => void;
  allItems: TreeViewItem[];
  iconMap?: TreeViewIconMap;
  menuItems?: TreeViewMenuItem[];
}

// Build a map of all items by ID
const buildItemMap = (items: TreeViewItem[]): Map<string, TreeViewItem> => {
  const map = new Map<string, TreeViewItem>();
  const processItem = (item: TreeViewItem) => {
    map.set(item.id, item);
    item.children?.forEach(processItem);
  };
  items.forEach(processItem);
  return map;
};

// Get all visible items (expanded)
const getVisibleItems = (
  items: TreeViewItem[],
  expandedIds: Set<string>
): TreeViewItem[] => {
  const result: TreeViewItem[] = [];
  const processItem = (item: TreeViewItem) => {
    result.push(item);
    if (item.children && expandedIds.has(item.id)) {
      item.children.forEach(processItem);
    }
  };
  items.forEach(processItem);
  return result;
};

// Add this default icon map
const defaultIconMap: TreeViewIconMap = {
  file: <Box className="h-4 w-4 text-red-600" />,
  folder: <Folder className="h-4 w-4 text-primary/80" />,
};

function TreeItem({
  item,
  depth = 0,
  selectedId,
  onSelect,
  expandedIds,
  onToggleExpand,
  getIcon,
  onAction,
  allItems,
  iconMap = defaultIconMap,
  menuItems,
}: TreeItemProps): JSX.Element {
  const isOpen = expandedIds.has(item.id);
  const isSelected = selectedId === item.id;
  const itemRef = useRef<HTMLDivElement>(null);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    onSelect(item.id);
    
    // Open folder on single click if it's a folder and already selected
    if (item.children && isSelected) {
      onToggleExpand(item.id, !isOpen);
    }
  };

  const handleAction = (action: string) => {
    if (onAction) {
      onAction(action, [item]);
    }
  };

  // Render the appropriate icon
  const renderIcon = () => {
    // If a custom getIcon function is provided, use it
    if (getIcon) {
      return getIcon(item, depth);
    }

    // If the item has a custom icon, use it
    if (item.icon) {
      return item.icon;
    }

    // Otherwise use the icon map
    if (item.children) {
      return iconMap.folder || defaultIconMap.folder;
    } else {
      return iconMap.file || defaultIconMap.file;
    }
  };

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div>
          <div
            ref={itemRef}
            data-tree-item
            data-id={item.id}
            data-depth={depth}
            data-folder-closed={item.children && !isOpen}
            className={`select-none cursor-pointer ${
              isSelected ? `bg-orange-100 rounded-md` : "text-foreground"
            } px-1`}
            style={{ paddingLeft: `${depth * 20}px` }}
            onClick={handleClick}
          >
            <div className="flex items-center h-8">
              {item.children ? (
                <div className="flex items-center gap-2 flex-1 group">
                  <Collapsible
                    open={isOpen}
                    onOpenChange={(open) => onToggleExpand(item.id, open)}
                  >
                    <CollapsibleTrigger
                      asChild
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <motion.div
                          initial={false}
                          animate={{ rotate: isOpen ? 90 : 0 }}
                          transition={{ duration: 0.1 }}
                        >
                          <ChevronRight className="h-4 w-4" />
                        </motion.div>
                      </Button>
                    </CollapsibleTrigger>
                  </Collapsible>
                  {renderIcon()}
                  <span className="flex-1">{item.name}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2 flex-1 pl-6">
                  {renderIcon()}
                  <span className="flex-1">{item.name}</span>
                </div>
              )}
            </div>
          </div>

          {item.children && (
            <Collapsible
              open={isOpen}
              onOpenChange={(open) => onToggleExpand(item.id, open)}
            >
              <AnimatePresence initial={false}>
                {isOpen && (
                  <CollapsibleContent forceMount asChild>
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.05 }}
                    >
                      {item.children?.map((child) => (
                        <TreeItem
                          key={child.id}
                          item={child}
                          depth={depth + 1}
                          selectedId={selectedId}
                          onSelect={onSelect}
                          expandedIds={expandedIds}
                          onToggleExpand={onToggleExpand}
                          getIcon={getIcon}
                          onAction={onAction}
                          allItems={allItems}
                          iconMap={iconMap}
                          menuItems={menuItems}
                        />
                      ))}
                    </motion.div>
                  </CollapsibleContent>
                )}
              </AnimatePresence>
            </Collapsible>
          )}
        </div>
      </ContextMenuTrigger>
      {menuItems && menuItems.length > 0 && (
        <ContextMenuContent className="w-64">
          {menuItems?.map((menuItem) => (
            <ContextMenuItem
              key={menuItem.id}
              onClick={() => {
                menuItem.action([item]);
              }}
            >
              {menuItem.icon && (
                <span className="mr-2 h-4 w-4">{menuItem.icon}</span>
              )}
              {menuItem.label}
            </ContextMenuItem>
          ))}
        </ContextMenuContent>
      )}
    </ContextMenu>
  );
}

export default function TreeView({
  className,
  data,
  iconMap,
  searchPlaceholder = "Search...",
  showExpandAll = true,
  getIcon,
  onSelectionChange,
  onAction,
  menuItems,
}: TreeViewProps) {
  const [expandedIds, setExpandedIds] = useState<Set<string>>(new Set());
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  const treeRef = useRef<HTMLDivElement>(null);

  // Create a map of all items by ID
  const itemMap = useMemo(() => buildItemMap(data), [data]);

  // Filter data based on search query
  const filteredData = useMemo(() => {
    if (!searchQuery.trim()) return data;

    const query = searchQuery.toLowerCase();
    const matches = new Set<string>();

    // Helper function to check if an item or any of its children match the query
    const checkMatch = (item: TreeViewItem): boolean => {
      const nameMatches = item.name.toLowerCase().includes(query);
      if (nameMatches) {
        matches.add(item.id);
        return true;
      }

      // Check children
      if (item.children) {
        const childMatches = item.children.some(checkMatch);
        if (childMatches) {
          matches.add(item.id);
          return true;
        }
      }

      return false;
    };

    // First pass: identify all matches
    data.forEach(checkMatch);

    // Second pass: filter the data to only include matches and their ancestors
    const filterItem = (item: TreeViewItem): TreeViewItem | null => {
      if (matches.has(item.id)) {
        // This item matches, include it and filter its children
        return {
          ...item,
          children: item.children
            ? item.children
                .map(filterItem)
                .filter((child): child is TreeViewItem => child !== null)
            : undefined,
        };
      }
      return null;
    };

    return data
      .map(filterItem)
      .filter((item): item is TreeViewItem => item !== null);
  }, [data, searchQuery]);

  // Function to collect all folder IDs
  const getAllFolderIds = (items: TreeViewItem[]): string[] => {
    let ids: string[] = [];
    items.forEach((item) => {
      if (item.children) {
        ids.push(item.id);
        ids = [...ids, ...getAllFolderIds(item.children)];
      }
    });
    return ids;
  };

  const handleExpandAll = () => {
    setExpandedIds(new Set(getAllFolderIds(data)));
  };

  const handleCollapseAll = () => {
    setExpandedIds(new Set());
  };

  const handleToggleExpand = (id: string, isOpen: boolean) => {
    const newExpandedIds = new Set(expandedIds);
    if (isOpen) {
      newExpandedIds.add(id);
    } else {
      newExpandedIds.delete(id);
    }
    setExpandedIds(newExpandedIds);
  };

  const handleSelect = (id: string) => {
    setSelectedId(id);
    if (onSelectionChange) {
      const selectedItem = itemMap.get(id);
      if (selectedItem) {
        onSelectionChange([selectedItem]);
      }
    }
  };

  // Add cleanup for mouse events - not necessary - we want the selection to stay
  // useEffect(() => {
  //   const handleClickAway = (e: MouseEvent) => {
  //     const target = e.target as Element;
  //     const clickedInside =
  //       (treeRef.current && treeRef.current.contains(target)) ||
  //       target.closest('[role="menu"]') ||
  //       target.closest("[data-radix-popper-content-wrapper]");

  //     if (!clickedInside) {
  //       setSelectedId(null);
  //       if (onSelectionChange) {
  //         onSelectionChange([]);
  //       }
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickAway);
  //   return () => document.removeEventListener("mousedown", handleClickAway);
  // }, [onSelectionChange]);

  return (
    <div className="flex gap-4">
      <div
        ref={treeRef}
        className="p-2"
      >
        <div className="h-10 flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-10 pl-9"
            />
          </div>
          {showExpandAll && (
            <div className="flex gap-2 shrink-0">
              <Button
                variant="ghost"
                size="sm"
                className="h-10 px-2"
                onClick={handleExpandAll}
              >
                <ChevronDown className="h-4 w-4 mr-1" />
                Expand All
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-10 px-2"
                onClick={handleCollapseAll}
              >
                <ChevronRight className="h-4 w-4 mr-1" />
                Collapse All
              </Button>
            </div>
          )}
        </div>

        <div
          className={cn(
            "rounded-lg bg-card relative select-none",
            className
          )}
        >
          {filteredData.map((item) => (
            <TreeItem
              key={item.id}
              item={item}
              selectedId={selectedId}
              onSelect={handleSelect}
              expandedIds={expandedIds}
              onToggleExpand={handleToggleExpand}
              getIcon={getIcon}
              onAction={onAction}
              allItems={data}
              iconMap={iconMap}
              menuItems={menuItems}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

