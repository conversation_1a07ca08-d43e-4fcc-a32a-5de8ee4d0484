{"name": "beatdetect.js", "version": "1.0.0", "description": "Beat detector for audio files. KISS, light and library free. ", "keywords": ["javascript", "es6-module", "bpm", "bpm-offset", "peak-detection"], "main": "src/BeatDetect.js", "scripts": {"build": "webpack --config webpack/webpack.prod.js ", "watch": "webpack --watch --config webpack/webpack.dev.js", "server": "http-server -p 1337 -c-1 -o example.html", "test": "karma start test/karma.config.js", "test-dev": "karma start test/karma.config.js --dev=true", "doc": "jsdoc -c doc/jsDoc.json", "beforecommit": "npm run test && npm run doc && npm run build"}, "homepage": "https://github.com/ArthurBeaulieu/BeatDetect.js", "repository": {"type": "git", "url": "git+https://github.com/ArthurBeaulieu/BeatDetect.js.git"}, "bugs": {"url": "https://github.com/ArthurBeaulieu/BeatDetect.js/issues"}, "author": "<PERSON>", "license": "GPL-3.0", "devDependencies": {"@babel/core": "^7.18.9", "@babel/preset-env": "^7.18.9", "babel-loader": "^8.2.5", "clean-webpack-plugin": "^4.0.0", "eslint": "^8.20.0", "eslint-webpack-plugin": "^3.2.0", "http-server": "^14.1.1", "jasmine": "^4.2.1", "jasmine-core": "^4.2.0", "jsdoc": "^3.6.10", "karma": "^6.4.0", "karma-coverage": "^2.2.0", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^5.1.0", "karma-webpack": "^5.0.0", "tui-jsdoc-template": "^1.2.2", "webpack": "^5.73.0", "webpack-cli": "^4.10.0", "webpack-merge": "^5.8.0"}}