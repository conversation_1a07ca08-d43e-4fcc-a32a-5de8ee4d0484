name: e2e

on:
  pull_request:

jobs:
  e2e:
    runs-on: ubuntu-latest
    name: E2E tests
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/yarn

      - uses: browser-actions/setup-chrome@v1

      - name: Install Cypress
        run: |
          ./node_modules/.bin/cypress install

      - uses: ./.github/workflows/build

      - uses: cypress-io/github-action@v4
        with:
          spec: cypress/e2e/*.cy.js
          browser: chrome
          record: false

      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
