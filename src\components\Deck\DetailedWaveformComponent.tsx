import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import WaveSurfer from 'wavesurfer.js'; // This now points to our local version
import TimelinePlugin from '../../../lib/wavesurfer.js/src/plugins/timeline';
import BeatgridPlugin from '../../../lib/wavesurfer.js/src/plugins/beatgrid';
import SegmentsPlugin from '../../../lib/wavesurfer.js/src/plugins/segments';
// import ZoomPlugin from '../../../lib/wavesurfer.js/src/plugins/zoom';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Card } from '@/components/ui/card';
import { useStore } from '@/contexts/StoreContext';
import { getTrackFileHandle } from '@/services/AudioAnalysisService';
import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import useWhyDidYouUpdate from '../useWhyDidYouUpdate';

// Define the segment structure
interface Segment {
  start: number;
  end: number;
  label: string;
}

interface WaveformComponentProps {
  deck: DeckStoreInstance;
  height?: number;
  showTimeline?: boolean; // Whether to show the timeline
  showBeatgrid?: boolean; // Whether to show the beatgrid
  showSegments?: boolean; // Whether to show the segments
  segments?: Segment[]; // Array of segments to display
  timeOffset?: number; // New parameter for beat grid time offset in seconds
  bpm?: number; // New parameter for manual BPM setting
  zoomable?: boolean;
}

const WaveformComponent: React.FC<WaveformComponentProps> = observer((props) => {
  const {
    deck,
    height = 80,
    zoomable = false,
    showTimeline = false,
    showBeatgrid = false,
    showSegments = false,
    segments = [],
    timeOffset,
    bpm
  } = props;
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isWaveSurferReady, setIsWaveSurferReady] = useState(false); // New state to track if wavesurfer is ready
  const [zoomLevel, setZoomLevel] = useState(0);
  const rootStore = useStore();

  useWhyDidYouUpdate(`WaveformComponent (Deck: ${deck.id})`, {
    deck,
    height,
    showTimeline,
    showBeatgrid,
    showSegments,
    segments,
    timeOffset,
    bpm,
    zoomable,
    // You can also include relevant state here if you want to see
    // if state changes are related to prop changes or effect re-runs.
    // e.g., internalStateValue
  });

  // Create and destroy WaveSurfer instance
  useEffect(() => {
    
    if (!waveformRef.current) return;
    if (!deck.loadedTrack) return;


    setIsWaveSurferReady(false); // Set to false when creating a new instance
    setIsLoading(true); // Set loading state when creating a new instance

    // Create plugins if needed
    const plugins = [];

    // Add timeline plugin if requested
    if (showTimeline) {
      const timelinePlugin = TimelinePlugin.create({
        height: 20,
        insertPosition: 'beforebegin',
        primaryLabelInterval: 5,
        secondaryLabelInterval: 1,
        style: {
          fontSize: '10px',
          color: '#555',
        },
      });
      plugins.push(timelinePlugin);
    }

    // Add zoom plugin if requested
    // if (detailed && zoomable) {
    //   const zoomPlugin = ZoomPlugin.create({
    //     // The amount of zoom per wheel step (0.5 means 50% magnification per scroll)
    //     scale: 0.5,
    //     // Maximum zoom level
    //     maxZoom: 500,
    //     // Smoothness of zooming
    //     deltaThreshold: 5,
    //   });
    //   plugins.push(zoomPlugin);
    // }

    // Add beatgrid plugin if requested and we have BPM data
    if (showBeatgrid && (deck.originalBpm > 0 || bpm)) {
      // Use the track's BPM and offset if available, otherwise use props
      const trackBpm = deck.originalBpm || 0;
      const trackOffset = deck.loadedTrack?.analysisData?.firstBeatTime || 0;

      const beatgridPlugin = BeatgridPlugin.create({
        height: 20,
        bpm: bpm || trackBpm || 120,
        offset: timeOffset !== undefined ? timeOffset : trackOffset,
        beatsPerBar: 4,
        showVerticalLines: true,
        fullHeightLines: true,
        showBarNumbers: true,
        showCurrentPosition: true,
        currentPositionPlacement: 'center',
        currentPositionColor: '#ccc',
        beatLineOpacity: 0.25,
        beatLineColor: '#0066cc',
        barLineColor: '#0033cc',
        beatLineWidth: 1,
        barLineWidth: 2,
      });
      plugins.push(beatgridPlugin);
    }

    // Add segments plugin if requested and we have segments data
    if (showSegments && segments.length > 0) {
      const segmentsPlugin = SegmentsPlugin.create({
        height: 20,
        segments: segments,
        // colors: {
        //   chorus: '#E91E63',
        //   verse: '#3F51B5',
        //   bridge: '#00BCD4',
        //   intro: '#2196F3',
        //   outro: '#FF9800',
        //   default: '#607D8B'
        // }
      });
      plugins.push(segmentsPlugin);
    }

    // Calculate adjusted height for waveform to account for plugins
    let waveformHeight = height;
    // Reduce waveform height to make room for plugins
    if (showTimeline) waveformHeight -= 20; // Timeline height
    if (showBeatgrid) waveformHeight -= 30; // Beatgrid height
    if (showSegments && segments.length > 0) waveformHeight -= 40; // Segments height
    // Ensure minimum height
    waveformHeight = Math.max(waveformHeight, 60);

    // Create WaveSurfer instance with type assertion to handle custom options
    const wavesurfer = WaveSurfer.create({
      container: waveformRef.current,
      height: waveformHeight,
      waveColor: 'rgba(100, 100, 100, 0.4)',
      progressColor: 'rgba(100, 100, 255, 0.8)',
      cursorColor: '#fff',
      // barWidth: detailed ? 1 : 2,
      // barGap: detailed ? 0 : 1,
      // barRadius: 2,
      normalize: true,
      interact: true,
      minPxPerSec: 100,
      fillParent: false,
      width: 'auto',
      plugins: plugins,
      playbackRate: deck.playbackRate,
      // Disable audio playback - we'll use our own audio engine
      media: new Audio(), // Use a dummy audio element
      mediaControls: false, // Disable media controls
      // Enable brightness-based coloring
      colorizeByBrightness: true,

      // Custom color stops for brightness values
      brightnessColors: [
        { stop: 0, color: 'rgb(255, 0, 0)' },     // Low brightness -> Red
        { stop: 0.25, color: 'rgb(255, 165, 0)' }, // Low-Mid -> Orange
        { stop: 0.5, color: 'rgb(255, 255, 0)' },  // Mid -> Yellow
        { stop: 0.75, color: 'rgb(0, 255, 0)' },   // Mid-High -> Green
        { stop: 1, color: 'rgb(0, 191, 255)' }     // High brightness -> Deep Sky Blue
      ],
    } as any); // Use type assertion to bypass TypeScript checking

    // not necessary - never played
    // // Disable volume control since we're not using Wavesurfer for audio playback
    // wavesurfer.setVolume(0);

    // Configure scrolling behavior after creation
    // For detailed view, we want horizontal scrolling
    waveformRef.current.style.overflowX = 'auto';
    waveformRef.current.style.overflowY = 'hidden';
    // Ensure container takes minimum space for detailed view
    waveformRef.current.style.width = 'auto'; // Allow width to be determined by minPxPerSec and track duration

    // Store the instance
    wavesurferRef.current = wavesurfer;

    // Set up event listeners
    wavesurfer.on('ready', () => {
      setIsLoading(false);
      setIsWaveSurferReady(true); // Set to true when wavesurfer is ready
      console.log('Waveform ready');

      // Apply initial zoom if detailed view
      if ( zoomable) {
        try {
          // Apply initial zoom level
          wavesurfer.zoom(2**zoomLevel * 50); // Scale factor for zoom

          // For detailed view, we need to ensure the container allows scrolling
          if (waveformRef.current) {
            waveformRef.current.style.overflowX = 'auto';
            waveformRef.current.style.overflowY = 'hidden';
          }
        } catch (error) {
          console.error('Error applying initial zoom:', error);
        }
      }
    });

    // Listen for zoom events from the plugin
    wavesurfer.on('zoom', (minPxPerSec) => {
      // Calculate and update zoom level based on minPxPerSec
      const calculatedZoomLevel = Math.log2(minPxPerSec / 50);
      setZoomLevel(Math.round(calculatedZoomLevel));
    });

    wavesurfer.on('error', (err) => {
      
      if (err instanceof MediaError && err.code === 4 && err.message === 'MEDIA_ELEMENT_ERROR: Empty src attribute') {
        // Do nothing
      } else {
        console.error('Waveform error:', err);
        setIsLoading(false);
        setIsWaveSurferReady(false);
      }
    });

    // Handle click events from the waveform visualization
    wavesurfer.on('interaction', (time) => {
      if (deck.loadedTrack) {
        // Use the deck's seek method which will update the audio engine
        deck.seek(time);
      }
    });

    // Load the track if available
    const loadTrack = async () => {
      
      if (deck.loadedTrack && deck.audioEngine) {
        try {
          if (!deck.audioEngine.isBufferLoaded()) {
            await deck.audioEngine.loadTrack(deck.loadedTrack.id);
          }
          
          
          const audioBuffer = deck.audioEngine.getAudioBuffer();
          if (audioBuffer) {
            const peaks = [];
            for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
              peaks.push(audioBuffer.getChannelData(i));
            }
            wavesurfer.load('', peaks, audioBuffer.duration);
          }
        } catch (error) {
          console.error('Error loading track in wavesurfer init:', error);
        }
      }
    };
    
    loadTrack();

    // Clean up on unmount
    return () => {
      
      wavesurfer.destroy();
      wavesurferRef.current = null;
      setIsWaveSurferReady(false); // Set to false on cleanup
    };
  }, [height, showTimeline, showBeatgrid, showSegments, timeOffset]);

  // Load audio when track changes
  useEffect(() => {
    console.log("main use effect")
    const loadTrackAudio = async () => {
      if (!wavesurferRef.current || !deck.loadedTrack) return;

      try {
        setIsLoading(true);
        setIsWaveSurferReady(false); // Set to false when starting to load new audio

        // Check if the audio engine already has the buffer loaded
        if (deck.audioEngine) {
          if(!deck.audioEngine.isBufferLoaded()){
            await deck.audioEngine.loadTrack(deck.loadedTrack.id);
          }
          // Get the audio buffer from the audio engine
          const audioBuffer = deck.audioEngine.getAudioBuffer();
          
          if (audioBuffer) {
            console.log(`Using existing audio buffer from audio engine for waveform visualization`);
            
            // Extract channel data from the audio buffer
            const peaks = [];
            for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
              peaks.push(audioBuffer.getChannelData(i));
            }
            
            // Load the peaks data into wavesurfer
            wavesurferRef.current.load('', peaks, audioBuffer.duration);
            return;
          }
        }
        
        // Fallback to loading from file if audio engine doesn't have the buffer
        const fileHandle = await getTrackFileHandle(rootStore, deck.loadedTrack.id);
        if (!fileHandle) {
          throw new Error(`Could not retrieve file handle for track ${deck.loadedTrack.id}`);
        }

        const file = await fileHandle.getFile();
        // Load the blob for visualization only
        wavesurferRef.current.loadBlob(file);

      } catch (error) {
        console.error('Error loading track audio for waveform:', error);
        setIsLoading(false);
        setIsWaveSurferReady(false); // Set to false on error
      }
    };

    loadTrackAudio();
  }, [deck.loadedTrack, rootStore]);

  // We don't need to sync playback state anymore since we're not using Wavesurfer for playback
  // Instead, we'll just update the visual playhead position based on deck.currentTime

  // Sync current time - always update the Wavesurfer playhead position
  // This is now the primary way we keep the visualization in sync with our audio engine
  useEffect(() => {
    if (!wavesurferRef.current || !deck.loadedTrack) return;

    // Always update the playhead position to match the deck's current time
    // We don't need to check for significant differences anymore since Wavesurfer
    // is not controlling playback and can't cause feedback loops
    wavesurferRef.current.setTime(deck.currentTime);

  }, [deck.currentTime, deck.loadedTrack]);

  // Also sync when playback state changes to ensure visual state matches
  useEffect(() => {
    if (!wavesurferRef.current || !deck.loadedTrack) return;

    // Update the playhead position when playback starts or stops
    wavesurferRef.current.setTime(deck.currentTime);

  }, [deck.isPlaying, deck.loadedTrack]);

  // Add an effect to update the waveform when playback rate changes
  useEffect(() => {
    if (!wavesurferRef.current || !deck.loadedTrack || !isWaveSurferReady) return;
    
     const handler = setTimeout(() => {
      if (!wavesurferRef.current || !deck.loadedTrack || !isWaveSurferReady) return;
      wavesurferRef.current.setPlaybackRate(deck.playbackRate);
     }, 200);

    return () => {
      clearTimeout(handler);
    };
  }, [deck.playbackRate, deck.loadedTrack, isWaveSurferReady]);

  // Handle zoom in
  const handleZoomIn = () => {
    if (!wavesurferRef.current || !deck.loadedTrack || !isWaveSurferReady) return;
    try {
      const newZoom = Math.min(zoomLevel + 1, 20);
      setZoomLevel(newZoom);
      wavesurferRef.current.zoom(2**newZoom * 50);
    } catch (error) {
      console.error('Error zooming in:', error);
    }
  };

  // Handle zoom out
  const handleZoomOut = () => {
    if (!wavesurferRef.current || !deck.loadedTrack || !isWaveSurferReady) return;
    try {
      const newZoom = Math.max(zoomLevel - 1, 0);
      setZoomLevel(newZoom);
      wavesurferRef.current.zoom(2**newZoom * 50);
    } catch (error) {
      console.error('Error zooming out:', error);
    }
  };

  // Handle reset zoom
  const handleResetZoom = () => {
    if (!wavesurferRef.current || !deck.loadedTrack || !isWaveSurferReady) return;
    try {
      setZoomLevel(1);
      wavesurferRef.current.zoom(50);
    } catch (error) {
      console.error('Error resetting zoom:', error);
    }
  };


  return (
    <Card className="relative overflow-hidden">
      <div className="p-1">
        {/* Waveform container */}
        <div className="relative">
          <div
            ref={waveformRef}
            className={`w-full ${isLoading ? 'opacity-50' : 'opacity-100'} transition-opacity`}
          />
        </div>

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/50">
            <span className="text-sm text-muted-foreground">Loading waveform...</span>
          </div>
        )}

        {zoomable && (
          <div className="flex items-center justify-between gap-1 mt-2 bg-muted/20 p-1 rounded-md">
            <div className="text-xs text-muted-foreground">
              Zoom: {zoomLevel}x
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="icon"
                className="h-7 w-7"
                onClick={handleZoomOut}
                disabled={zoomLevel <= 0 || isLoading || !deck.loadedTrack || !isWaveSurferReady}
              >
                <ZoomOut className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-7 w-7"
                onClick={handleZoomIn}
                disabled={zoomLevel >= 20 || isLoading || !deck.loadedTrack || !isWaveSurferReady}
              >
                <ZoomIn className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-7 w-7"
                onClick={handleResetZoom}
                disabled={zoomLevel === 0 || isLoading || !deck.loadedTrack || !isWaveSurferReady}
              >
                <RotateCcw className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
});

export default WaveformComponent;



















