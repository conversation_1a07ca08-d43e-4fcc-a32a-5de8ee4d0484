<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Class: BeatDetect | BeatDetect.js</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/bootstrap.min.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify-jsdoc.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/tui-doc.css">

    
</head>
<body>
<nav class="lnb" id="lnb">
    <div class="logo" style="">
        
            <img src="img/toast-ui.png" width="100%" height="100%">
        
    </div>
    <div class="title">
        <h1><a href="index.html" class="link">BeatDetect.js</a></h1>
        
            <span class="version">v1.0.0</span>
        
    </div>
    <div class="search-container" id="search-container">
        <input type="text" placeholder="Search">
        <ul></ul>
    </div>
    
    <div class="lnb-api hidden"><h3>Classes</h3><ul><li><a href="BeatDetect.html">BeatDetect</a><button type="button" class="hidden toggle-subnav btn btn-link">  <span class="glyphicon glyphicon-plus"></span></button><div class="hidden" id="BeatDetect_sub"><div class="member-type">Members</div><ul class="inner"><li><a href="BeatDetect.html#_bpmRange">_bpmRange</a></li><li><a href="BeatDetect.html#_float">_float</a></li><li><a href="BeatDetect.html#_highPassFreq">_highPassFreq</a></li><li><a href="BeatDetect.html#_log">_log</a></li><li><a href="BeatDetect.html#_lowPassFreq">_lowPassFreq</a></li><li><a href="BeatDetect.html#_perf">_perf</a></li><li><a href="BeatDetect.html#_round">_round</a></li><li><a href="BeatDetect.html#_sampleRate">_sampleRate</a></li><li><a href="BeatDetect.html#_tapResetId">_tapResetId</a></li><li><a href="BeatDetect.html#_timeSignature">_timeSignature</a></li><li><a href="BeatDetect.html#_ts">_ts</a></li><li><a href="BeatDetect.html#count">count</a></li><li><a href="BeatDetect.html#float">float</a></li><li><a href="BeatDetect.html#highPassFreq">highPassFreq</a></li><li><a href="BeatDetect.html#log">log</a></li><li><a href="BeatDetect.html#lowPassFreq">lowPassFreq</a></li><li><a href="BeatDetect.html#perf">perf</a></li><li><a href="BeatDetect.html#round">round</a></li><li><a href="BeatDetect.html#sampleRate">sampleRate</a></li><li><a href="BeatDetect.html#VERSION">VERSION</a></li></ul><div class="member-type">Methods</div><ul class="inner"><li><a href="BeatDetect.html#._buildOfflineCtx">_buildOfflineCtx</a></li><li><a href="BeatDetect.html#._fetchRawTrack">_fetchRawTrack</a></li><li><a href="BeatDetect.html#._floatRound">_floatRound</a></li><li><a href="BeatDetect.html#._getIntervals">_getIntervals</a></li><li><a href="BeatDetect.html#._getLowestTimeOffset">_getLowestTimeOffset</a></li><li><a href="BeatDetect.html#._getOffsets">_getOffsets</a></li><li><a href="BeatDetect.html#._getPeaks">_getPeaks</a></li><li><a href="BeatDetect.html#._getPerfDuration">_getPerfDuration</a></li><li><a href="BeatDetect.html#._logEvent">_logEvent</a></li><li><a href="BeatDetect.html#._processRenderedBuffer">_processRenderedBuffer</a></li><li><a href="BeatDetect.html#._tapBpm">_tapBpm</a></li><li><a href="BeatDetect.html#.getBeatInfo">getBeatInfo</a></li><li><a href="BeatDetect.html#.tapBpm">tapBpm</a></li></ul></div></li></ul></div>
</nav>
<div id="resizer"></div>

<div class="main" id="main">
    




<section>

<header>
    
        <h2><span class="attribs"><span class="type-signature"></span></span>BeatDetect<span class="signature">(options<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h2>
        
    
</header>

<article>
    
        <div class="container-overview">
        
            

<dt>
    
        <h4 class="name" id="BeatDetect">
            
                new <span class="type-signature"></span>BeatDetect<span class="signature">(options<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line22">line 22</a></code>
            </div>
            
        </h4>

        
        <p class="summary"><h1>Beat detection library</h1></p>
        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This library provides an audio analyser to retrieve a track beat per minute value. It is
also made to guess the time offset before the first significant sound (relative to the BPM), and the time offset of
the estimated first bar. All its parameters can be updated when constructed, so it can adapt to any type of audio
input. The analysis method is based on the work of Joe Sullivan and José M. Pérez, and is describe in the
<code>README.md</code> file of its repository. Remarks and pull requests are welcome!</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The configuration object of this library</p>
                <h6>Properties</h6>
                
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>log</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last"><p>Log debug information during the analysis process</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>perf</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last"><p>Log ellapsed time of each analysis step</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>sampleRate</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    44100
                
                </td>
            

            <td class="description last"><p>The sample rate to use during the audio analysis. Can be changed its setter</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>round</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last"><p>Allow the output rounding to remove floating point</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>float</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    8
                
                </td>
            

            <td class="description last"><p>The floating precision for the output. Disabled if round is at true</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lowPassFreq</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    150
                
                </td>
            

            <td class="description last"><p>The low pass filter cut frequency</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>highPassFreq</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    100
                
                </td>
            

            <td class="description last"><p>The high pass filter cut frequency</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bpmRange</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;number></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    [90, 180]
                
                </td>
            

            <td class="description last"><p>The BPM range to output the result in</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeSignature</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    4
                
                </td>
            

            <td class="description last"><p>The analysed audio time signature</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    

    


<dl class="details">

    

    
    <dt class="tag-since">Since:</dt>
    <dd class="tag-since"><ul class="dummy"><li>November 2020</li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-author">Author:</dt>
    <dd class="tag-author">
        <ul>
            <li>Arthur Beaulieu</li>
        </ul>
    </dd>
    

    

    

    

    

    

    
</dl>


    

</dd>

        
        </div>
    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        <dl>
            

<dt>
<h4 class="name" id="_bpmRange">
    <span class="type-signature"><span class="icon green">private</span> </span>_bpmRange<span class="type-signature"> :array</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line58">line 58</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The BPM range to display the output in</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">array</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_float">
    <span class="type-signature"><span class="icon green">private</span> </span>_float<span class="type-signature"> :number</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line49">line 49</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The number of floating point for the output</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">number</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_highPassFreq">
    <span class="type-signature"><span class="icon green">private</span> </span>_highPassFreq<span class="type-signature"> :number</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line55">line 55</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The high pass filter cut frequency</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">number</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_log">
    <span class="type-signature"><span class="icon green">private</span> </span>_log<span class="type-signature"> :boolean</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line36">line 36</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Log debug information in the console when set to true</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">boolean</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_lowPassFreq">
    <span class="type-signature"><span class="icon green">private</span> </span>_lowPassFreq<span class="type-signature"> :number</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line52">line 52</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The low pass filter cut frequency</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">number</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_perf">
    <span class="type-signature"><span class="icon green">private</span> </span>_perf<span class="type-signature"> :boolean</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line40">line 40</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Log elapsed times during the analysis in the console when set to true</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">boolean</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_round">
    <span class="type-signature"><span class="icon green">private</span> </span>_round<span class="type-signature"> :boolean</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line46">line 46</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Remove any floating point from output when set to true</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">boolean</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_sampleRate">
    <span class="type-signature"><span class="icon green">private</span> </span>_sampleRate<span class="type-signature"> :number</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line43">line 43</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The sample rate used for analysis. Must match the analysed audio sample rate</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">number</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_tapResetId">
    <span class="type-signature"><span class="icon green">private</span> </span>_tapResetId<span class="type-signature"> :number</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line75">line 75</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Reset tap timeout ID</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">number</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_timeSignature">
    <span class="type-signature"><span class="icon green">private</span> </span>_timeSignature<span class="type-signature"> :number</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line61">line 61</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The studied track time signature</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">number</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="_ts">
    <span class="type-signature"><span class="icon green">private</span> </span>_ts<span class="type-signature"> :object</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line68">line 68</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Contains timestamp used to determine manual BPM</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">object</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="count">
    <span class="type-signature"><span class="icon green">private</span> </span>count<span class="type-signature"> :number</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line65">line 65</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The amount of time a click is trigerred to compute BPM</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">number</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="float">
    <span class="type-signature"></span>float<span class="type-signature"></span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line609">line 609</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Set the output floating precision.</p>
    </div>
    

    <!--
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="highPassFreq">
    <span class="type-signature"></span>highPassFreq<span class="type-signature"></span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line623">line 623</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Set the high pass filter cut frequency.</p>
    </div>
    

    <!--
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="log">
    <span class="type-signature"></span>log<span class="type-signature"></span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line588">line 588</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Set logging in console.</p>
    </div>
    

    <!--
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="lowPassFreq">
    <span class="type-signature"></span>lowPassFreq<span class="type-signature"></span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line616">line 616</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Set the low pass filter cut frequency.</p>
    </div>
    

    <!--
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="perf">
    <span class="type-signature"></span>perf<span class="type-signature"></span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line595">line 595</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Set performance timings in console.</p>
    </div>
    

    <!--
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="round">
    <span class="type-signature"></span>round<span class="type-signature"></span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line602">line 602</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Set output rounding.</p>
    </div>
    

    <!--
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="sampleRate">
    <span class="type-signature"></span>sampleRate<span class="type-signature"></span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line581">line 581</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>Set sample rate for analysis.</p>
    </div>
    

    <!--
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        
            

<dt>
<h4 class="name" id="VERSION">
    <span class="type-signature"></span>VERSION<span class="type-signature"> :string</span>
    
    <div class="container-source members">
        <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
        <code><a href="BeatDetect.js.html#line33">line 33</a></code>
    </div>
    
</h4>


</dt>
<dd>
    
    <div class="description">
        <p>The BeatDetect version number</p>
    </div>
    

    <!--
    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">string</span>


            </li>
        </ul>
    
    -->

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
                

<dt>
    
        <h4 class="name" id="._buildOfflineCtx">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_buildOfflineCtx<span class="signature">(options)</span><span class="type-signature"> &rarr; {promise}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line150">line 150</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will build and connect all required nodes to perform the BPM analysis.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last"><p>The option object sent to the <code>_fetchRawTrack</code> method, augmented with track array buffer</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">promise</span>





- <p>A Promise that is resolved when analysis is done, of will be rejected otherwise</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._fetchRawTrack">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_fetchRawTrack<span class="signature">(options)</span><span class="type-signature"> &rarr; {promise}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line117">line 117</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will perform a fetch on the given URL to retrieve the track to analyse.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last"><p>The option object sent to the <code>getBeatInfo</code> method, augmented with performance marks</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">promise</span>





- <p>A Promise that is resolved when analysis is done, of will be rejected otherwise</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._floatRound">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_floatRound<span class="signature">(value, precision)</span><span class="type-signature"> &rarr; {number}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line560">line 560</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will return a rounded floating value to a given precision.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The value to round at a given floating point</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>precision</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The amount of numbers after the floating point</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">number</span>





- <p>The rounded value with its given floating point</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._getIntervals">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_getIntervals<span class="signature">(peaks)</span><span class="type-signature"> &rarr; {array}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line297">line 297</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will then compute time interval between peak, in order to
spot the interval that is the most represented. See implementation for further details.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>peaks</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;object></span>


            
            </td>

            

            

            <td class="description last"><p>The peaks for a given track. Returned from _getPeaks method</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">array</span>





- <p>An array of time intervals</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._getLowestTimeOffset">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_getLowestTimeOffset<span class="signature">(position, bpm)</span><span class="type-signature"> &rarr; {object}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line431">line 431</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will search for the smallest time in track for a beat ; using
the estimated bpm, we rewind from time signature to get the closest from the track beginning.
See implementation for further details.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>position</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;object></span>


            
            </td>

            

            

            <td class="description last"><p>The beat position for beat to lower</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bpm</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The most credible BPM, computed after the most frequent time interval</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">object</span>





- <p>The beat offset and the offset to the first bar</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._getOffsets">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_getOffsets<span class="signature">(data, bpm)</span><span class="type-signature"> &rarr; {object}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line358">line 358</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will finally compute time offset from song start to first bar, or first
significant beat. See implementation for further details.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>data</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;object></span>


            
            </td>

            

            

            <td class="description last"><p>Array containg L audio data (no important to stereo this)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bpm</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The most credible BPM, computed after the most frequent time interval</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">object</span>





- <p>The beat offset and the offset to the first bar</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._getPeaks">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_getPeaks<span class="signature">(data)</span><span class="type-signature"> &rarr; {array}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line251">line 251</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will extract peak value from given channel data. See implementation for further details.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>data</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;array></span>


            
            </td>

            

            

            <td class="description last"><p>Array containg L/R audio data arrays</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">array</span>





- <p>An array filled with peaks value</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._getPerfDuration">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_getPerfDuration<span class="signature">(perf)</span><span class="type-signature"> &rarr; {object}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line464">line 464</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will format performance mark to be readable as times</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>perf</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;object></span>


            
            </td>

            

            

            <td class="description last"><p>The performance mark to format</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">object</span>





- <p>The ellapsed times for different beat detection steps</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._logEvent">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_logEvent<span class="signature">(level, string)</span><span class="type-signature"></span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line546">line 546</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will display a given console output if the logging is allowed.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last"><p>The console method to call in info, log, warn, error, trace etc.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>string</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last"><p>The text to display in the console</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._processRenderedBuffer">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_processRenderedBuffer<span class="signature">(options)</span><span class="type-signature"> &rarr; {promise}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line204">line 204</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>This method will process the audio buffer to extract its peak and guess the track BPM and offset.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last"><p>The option object sent to the <code>_buildOfflineCtx</code> method, augmented with track audio buffer</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">promise</span>





- <p>A Promise that is resolved when analysis is done, of will be rejected otherwise</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id="._tapBpm">
            
                <span class="type-signature"><span class="icon green">private</span>, <span class="icon green">static</span> </span>_tapBpm<span class="signature">(options, precision, callback)</span><span class="type-signature"></span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line502">line 502</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>Internal method to determine manual BPM</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last"><p>The internal options object</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>precision</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The floating point for result</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>callback</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>


            
            </td>

            

            

            <td class="description last"><p>The callback function to call each click</p></td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id=".getBeatInfo">
            
                <span class="type-signature"><span class="icon green">static</span> </span>getBeatInfo<span class="signature">(options)</span><span class="type-signature"> &rarr; {promise}</span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line84">line 84</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>Perform a beat detection on a given track and return the analysis result trhough the
Promise resolution. Any exception will be thrown in the Promise catch method.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last"><p>The beat detection option</p>
                <h6>Properties</h6>
                
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>url</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The url to the audio file to analyse</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The track name, only useful for logging</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    
    <div class="container-returns">
        <h5>Returns:</h5>
        
                



<span class="param-type">promise</span>





- <p>A Promise that is resolved when analysis is done, of will be rejected otherwise</p>




            
    </div>
    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            
                

<dt>
    
        <h4 class="name" id=".tapBpm">
            
                <span class="type-signature"><span class="icon green">static</span> </span>tapBpm<span class="signature">(options)</span><span class="type-signature"></span>
                
            
            <div class="container-source members">
                <code><a href="BeatDetect.js.html">BeatDetect.js</a></code>,
                <code><a href="BeatDetect.js.html#line487">line 487</a></code>
            </div>
            
        </h4>

        
    
</dt>
<dd>

    
    <div class="description">
        <blockquote>Providing a DOM element and a callback to manually determine a bpm, using a click.
After 5 seconds, the result will be reset.</blockquote>
    </div>
    

    

    

    

    
    <div class="container-params">
        <h5>Parameters:</h5>
        
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">objects</span>


            
            </td>

            

            

            <td class="description last"><p>Manual bpm determinitation options</p>
                <h6>Properties</h6>
                
<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>element</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last"><p>The DOM element to listen to</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>precision</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The floating point for result</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>callback</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>


            
            </td>

            

            

            <td class="description last"><p>The callback function to call each click</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    </div>
    

    

    

    

    

    

    

    


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

</dd>

            </dl>
    

    

    
</article>

</section>




</div>

<footer>
    <img class="logo" src="img/toast-ui.png" style="">
    <div class="footer-text">BeatDetect.js</div>
</footer>
<script>prettyPrint();</script>
<script src="scripts/jquery.min.js"></script>
<script src="scripts/tui-doc.js"></script>
<script src="scripts/linenumber.js"></script>

    <script>
        var id = 'BeatDetect_sub'.replace(/"/g, '_');
        var selectedApi = document.getElementById(id); // do not use jquery selector
        var $selectedApi = $(selectedApi);

        $selectedApi.removeClass('hidden');
        $selectedApi.parent().find('.glyphicon').removeClass('glyphicon-plus').addClass('glyphicon-minus');
        showLnbApi();
    </script>

</body>
</html>