<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Home | BeatDetect.js</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/bootstrap.min.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify-jsdoc.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/tui-doc.css">

    
</head>
<body>
<nav class="lnb" id="lnb">
    <div class="logo" style="">
        
            <img src="img/toast-ui.png" width="100%" height="100%">
        
    </div>
    <div class="title">
        <h1><a href="index.html" class="link">BeatDetect.js</a></h1>
        
            <span class="version">v1.0.0</span>
        
    </div>
    <div class="search-container" id="search-container">
        <input type="text" placeholder="Search">
        <ul></ul>
    </div>
    
    <div class="lnb-api hidden"><h3>Classes</h3><ul><li><a href="BeatDetect.html">BeatDetect</a><button type="button" class="hidden toggle-subnav btn btn-link">  <span class="glyphicon glyphicon-plus"></span></button><div class="hidden" id="BeatDetect_sub"><div class="member-type">Members</div><ul class="inner"><li><a href="BeatDetect.html#_bpmRange">_bpmRange</a></li><li><a href="BeatDetect.html#_float">_float</a></li><li><a href="BeatDetect.html#_highPassFreq">_highPassFreq</a></li><li><a href="BeatDetect.html#_log">_log</a></li><li><a href="BeatDetect.html#_lowPassFreq">_lowPassFreq</a></li><li><a href="BeatDetect.html#_perf">_perf</a></li><li><a href="BeatDetect.html#_round">_round</a></li><li><a href="BeatDetect.html#_sampleRate">_sampleRate</a></li><li><a href="BeatDetect.html#_tapResetId">_tapResetId</a></li><li><a href="BeatDetect.html#_timeSignature">_timeSignature</a></li><li><a href="BeatDetect.html#_ts">_ts</a></li><li><a href="BeatDetect.html#count">count</a></li><li><a href="BeatDetect.html#float">float</a></li><li><a href="BeatDetect.html#highPassFreq">highPassFreq</a></li><li><a href="BeatDetect.html#log">log</a></li><li><a href="BeatDetect.html#lowPassFreq">lowPassFreq</a></li><li><a href="BeatDetect.html#perf">perf</a></li><li><a href="BeatDetect.html#round">round</a></li><li><a href="BeatDetect.html#sampleRate">sampleRate</a></li><li><a href="BeatDetect.html#VERSION">VERSION</a></li></ul><div class="member-type">Methods</div><ul class="inner"><li><a href="BeatDetect.html#._buildOfflineCtx">_buildOfflineCtx</a></li><li><a href="BeatDetect.html#._fetchRawTrack">_fetchRawTrack</a></li><li><a href="BeatDetect.html#._floatRound">_floatRound</a></li><li><a href="BeatDetect.html#._getIntervals">_getIntervals</a></li><li><a href="BeatDetect.html#._getLowestTimeOffset">_getLowestTimeOffset</a></li><li><a href="BeatDetect.html#._getOffsets">_getOffsets</a></li><li><a href="BeatDetect.html#._getPeaks">_getPeaks</a></li><li><a href="BeatDetect.html#._getPerfDuration">_getPerfDuration</a></li><li><a href="BeatDetect.html#._logEvent">_logEvent</a></li><li><a href="BeatDetect.html#._processRenderedBuffer">_processRenderedBuffer</a></li><li><a href="BeatDetect.html#._tapBpm">_tapBpm</a></li><li><a href="BeatDetect.html#.getBeatInfo">getBeatInfo</a></li><li><a href="BeatDetect.html#.tapBpm">tapBpm</a></li></ul></div></li></ul></div>
</nav>
<div id="resizer"></div>

<div class="main" id="main">
    



    









    


    <section>
        <article class="readme"><h1>BeatDetect.js</h1>
<p><img src="https://badgen.net/badge/version/1.0.0/blue" alt="">
<a href="https://github.com/ArthurBeaulieu/BeatDetect.js/blob/master/LICENSE.md"><img src="https://img.shields.io/github/license/ArthurBeaulieu/BeatDetect.js.svg" alt="License"></a>
<img src="https://badgen.net/badge/documentation/written/green" alt="">
<img src="https://badgen.net/badge/test/basic/green" alt="">
<img src="https://badgen.net/badge/dependencies/none/green" alt=""></p>
<p><code>BeatDetect.js</code> is a JavaScript ES6 component that calculates the BPM of a track, with its time offset and the time offset to its first true beat. There are several options to fine tune the process, but <code>BeatDetect.js</code> works best with modern music (even better on EDM music), based on 4/4 time signature.</p>
<p>With ~5Ko minified, <code>BeatDetect.js</code> is designed to be stable and remain as light as possible. It is meant to be used client side ; unfortunately, the web audio API is not supported in nodejs.</p>
<p><a href="https://arthurbeaulieu.github.io/BeatDetect.js/example.html">Try me here!</a></p>
<h1>Get Started</h1>
<p>This repository was made to store documentation, test bench and source code. If you want to include this component in your project, you either need the <code>src/BeatDetect.js</code> file if you have an assets bundler in your project, or use the <code>dist/BeatDetect.min.js</code> to use the minified component. This minified file is compiled in ES5 JavaScript for compatibility reasons. The unminified file is, in the contrary, coded in ES6 JavaScript.</p>
<p>You must first instantiate a <code>BeatDetect.js</code> component, with several options as follows (none of them are mandatory, here are presented the default values) :</p>
<pre class="prettyprint source lang-javascript"><code>import BeatDetect from './src/BeatDetect.js';
// Component presented with default values
const beatDetect = new BeatDetect({
  sampleRate: 44100, // Most track are using this sample rate
  log: false, // Debug BeatDetect execution with logs
  perf: false, // Attach elapsed time to result object
  round: false, // To have an integer result for the BPM
  float: 4, // The floating precision in [1, Infinity]
  lowPassFreq: 150, // Low pass filter cut frequency
  highPassFreq: 100, // High pass filter cut frequency
  bpmRange: [90, 180], // The BPM range to output
  timeSignature: 4 // The number of beat in a measure
});
</code></pre>
<p>Once the instance is created, you can now analyse track by sending the track url to the <code>getBeatInfo()</code> public method :</p>
<pre class="prettyprint source lang-javascript"><code>beatDetect.getBeatInfo({
  url: `./demo/audio/Teminite - Don't Stop.mp3`,
  name: `Don't Stop from Teminite` // Optional argument, only for logging
}).then(info => {
  console.log(info.bpm); // 140
  console.log(info.offset); // 0.1542
  console.log(info.firstBar); // 0.1.8722
}).catch(error => {
  // The error string
});
</code></pre>
<p>Since the processing is asynchronous, you can call it in loops, but beware that it implies a significant load on the CPU. Because the <code>getBeatInfo()</code> method returns a <code>Promise</code>, it might be a better idea to chain them!</p>
<p>You can test those values in a mixing software, or in a <a href="https://github.com/ArthurBeaulieu/AudioVisualizer">AudioVisualizer</a>'s timeline component, that draws a waveform into a canvas with beat bars that scrolls over playback, the same used in DJ softwares! Just instantiate it with the provided BPM and firstBar values to check their accuracy.</p>
<p>This component also provides a method to manually determine a BPM using the mouse click :</p>
<pre class="prettyprint source lang-javascript"><code>beatDetect.tapBpm({
  element: document.getElementById('my-clickable-element'),
  precision: 4, // Floating point for result
  callback: bpm => {
    // Do whatever you want with it
  }
});
</code></pre>
<p>This repository includes examples on how to use this component with hardcoded urls, or with tracks selected by the user (see <a href="https://github.com/ArthurBeaulieu/BeatDetect.js/blob/main/example.html">example.html</a>).</p>
<p>You're now good to go! If however you need more information, you can read the online <a href="https://arthurbeaulieu.github.io/BeatDetect.js/doc/">documentation</a>.</p>
<h1>Development</h1>
<p>If you clone this repository, you can <code>npm install</code> to install development dependencies. This will allow you to build dist file, run the component tests or generate the documentation ;</p>
<ul>
<li><code>npm run build</code> to generate the minified file ;</li>
<li><code>npm run dev</code> to watch for any change in source code ;</li>
<li><code>npm run server</code> to launch a local development server ;</li>
<li><code>npm run doc</code> to generate documentation ;</li>
<li><code>npm run test</code> to run unit tests ;</li>
<li><code>npm run beforecommit</code> to perform tests, generate doc and bundle the JavaScript.</li>
</ul>
<p>To avoid CORS when locally loading the example HTML file, run the web server. Please do not use it on a production environment. Unit tests are performed on both Firefox and Chrome ; ensure you have both installed before running tests, otherwise they might fail.</p>
<p>If you have any question or idea, feel free to DM or open an issue (or even a PR, who knows) ! I'll be glad to answer your request.</p>
<h1>Credits</h1>
<p>This component is based on the method described by <a href="http://joesul.li/van/beat-detection-using-web-audio/">Joe Sullivan</a>, that renders and filters the track to keep its beats, then calculate intervals between those peaks and assumes that the BPM is based on the interval that is the most represented. The implementation is based on algorithms designed by <a href="https://jmperezperez.com/bpm-detection-javascript/">José M. Pérez</a> and adds the offset layer.</p></article>
    </section>






</div>

<footer>
    <img class="logo" src="img/toast-ui.png" style="">
    <div class="footer-text">BeatDetect.js</div>
</footer>
<script>prettyPrint();</script>
<script src="scripts/jquery.min.js"></script>
<script src="scripts/tui-doc.js"></script>
<script src="scripts/linenumber.js"></script>

    <script>
        var id = 'package:beatdetect.js_sub'.replace(/"/g, '_');
        var selectedApi = document.getElementById(id); // do not use jquery selector
        var $selectedApi = $(selectedApi);

        $selectedApi.removeClass('hidden');
        $selectedApi.parent().find('.glyphicon').removeClass('glyphicon-plus').addClass('glyphicon-minus');
        showLnbApi();
    </script>

</body>
</html>